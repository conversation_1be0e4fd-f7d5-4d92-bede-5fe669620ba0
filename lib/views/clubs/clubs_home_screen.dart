import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/providers/clubs_home_provider.dart';
import 'package:eljunto/views/clubs/widgets/club_invitation_button.dart';
import 'package:eljunto/views/clubs/widgets/club_list_view.dart';
import 'package:eljunto/views/clubs/widgets/club_section_header.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../reusableWidgets/appbar.dart';

class ClubsHomeScreen extends StatefulWidget {
  const ClubsHomeScreen({super.key});

  @override
  State<ClubsHomeScreen> createState() => _ClubsHomeScreenState();
}

class _ClubsHomeScreenState extends State<ClubsHomeScreen> {
  final ScrollController _standingClubScrollController = ScrollController();
  final ScrollController _impromptuClubScrollController = ScrollController();
  late ClubsHomeProvider _clubsHomeProvider;

  @override
  void initState() {
    super.initState();
    _clubsHomeProvider = Provider.of<ClubsHomeProvider>(context, listen: false);
    _standingClubScrollController.addListener(_onStandingScroll);
    _impromptuClubScrollController.addListener(_onImpromptuScroll);

    // Initialize the provider after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _clubsHomeProvider.initialize(context);
    });
  }

  // Scroll handlers for pagination
  void _onStandingScroll() {
    if (_standingClubScrollController.position.pixels >=
        _standingClubScrollController.position.maxScrollExtent) {
      _clubsHomeProvider.handleStandingClubScroll(context);
    }
  }

  void _onImpromptuScroll() {
    if (_impromptuClubScrollController.position.pixels >=
        _impromptuClubScrollController.position.maxScrollExtent) {
      _clubsHomeProvider.handleImpromptuClubScroll(context);
    }
  }

  // Refresh data
  Future<void> _refreshData() async {
    await _clubsHomeProvider.refresh(context);
  }

  @override
  void dispose() {
    _impromptuClubScrollController.dispose();
    _standingClubScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppConstants.bgImagePath),
          filterQuality: FilterQuality.high,
          fit: BoxFit.cover,
        ),
      ),
      child: Consumer<ClubsHomeProvider>(
        builder: (context, provider, child) {
          return Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            enabled: provider.isInitializing,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(80),
                child: Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                  child: const AppBarWidget(
                    appBarText: 'Clubs',
                  ),
                ),
              ),
              body: provider.hasError
                  ? _buildErrorState(provider.errorMessage)
                  : RefreshIndicator(
                      onRefresh: _refreshData,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 25),
                            IncomingClubInvitationButton(
                              loggedInUserId: provider.loggedInUserId,
                            ),
                            const SizedBox(height: 25),
                            const OutgoingClubRequestButton(),
                            const SizedBox(height: 25),
                            const Skeleton.replace(
                              replacement: Divider(
                                thickness: 2,
                                color: AppConstants.skeletonBackgroundColor,
                              ),
                              child: Divider(
                                thickness: 2,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 28),
                            const ClubSectionHeader(
                              title: "Standing Book Clubs",
                              clubType: ClubType.standing,
                            ),
                            _buildStandingClubsSection(provider),
                            const SizedBox(height: 25),
                            const Skeleton.replace(
                              replacement: Divider(
                                thickness: 2,
                                color: AppConstants.skeletonBackgroundColor,
                              ),
                              child: Divider(
                                thickness: 2,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 28),
                            const ClubSectionHeader(
                              title: "Impromptu Book Clubs",
                              clubType: ClubType.impromptu,
                            ),
                            _buildImpromptuClubsSection(provider),
                            const SizedBox(height: 25),
                          ],
                        ),
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  // Error state widget
  Widget _buildErrorState(String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: lbBold.copyWith(fontSize: 18),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              style: lbRegular.copyWith(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => _clubsHomeProvider.clearError(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  // Standing clubs section
  Widget _buildStandingClubsSection(ClubsHomeProvider provider) {
    return StandingClubListView(
      clubList: provider.standingBookClubList,
      scrollController: _standingClubScrollController,
      loggedInUserId: provider.loggedInUserId,
      userProfilePicture: provider.userProfilePicture,
      userHandle: provider.userHandle,
      onScrollEnd: () => provider.handleStandingClubScroll(context),
    );
  }

  // Impromptu clubs section
  Widget _buildImpromptuClubsSection(ClubsHomeProvider provider) {
    return ImpromptuClubListView(
      clubList: provider.impromptuBookClubList,
      scrollController: _impromptuClubScrollController,
      loggedInUserId: provider.loggedInUserId,
      userProfilePicture: provider.userProfilePicture,
      userHandle: provider.userHandle,
      onScrollEnd: () => provider.handleImpromptuClubScroll(context),
    );
  }
}
