import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/club_ui/impromptu_club_info.dart';
import 'package:eljunto/reusableWidgets/club_ui/standing_club_info.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusable_api_function/club/club_function.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../controller/notification_controller.dart';
import '../../controller/user_controller.dart';
import '../../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../../reusableWidgets/appbar.dart';

class ClubsHomeScreen extends StatefulWidget {
  const ClubsHomeScreen({super.key});

  @override
  State<ClubsHomeScreen> createState() => _ClubsHomeScreenState();
}

class _ClubsHomeScreenState extends State<ClubsHomeScreen> {
  int? loggedinUserId;
  Future? loadAPIs;
  String userProfilePicture = '';
  String userHandle = '';
  List<BookClubModel>? standingBookClubList = [];
  List<BookClubModel>? impromptuBookClubList = [];
  BookClubModel? bookCaseModel;
  bool isReopened = false;
  ClubController? clubController;
  BookClubController? bookClubController;
  NotificationController? notificationController;
  late UserController userController;
  final ScrollController _standingClubscrollController = ScrollController();
  final ScrollController _impromClubScrollController = ScrollController();

  @override
  void initState() {
    userController = Provider.of<UserController>(context, listen: false);
    notificationController =
        Provider.of<NotificationController>(context, listen: false);
    clubController = Provider.of(context, listen: false);
    _standingClubscrollController.addListener(_standingOnScroll);
    _impromClubScrollController.addListener(_impromptuOnScroll);
    super.initState();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
    await getUserDetails();
  }

  Future<void> getUserDetails() async {
    try {
      // final responseMap =
      // isLoading = true;
      await Provider.of<UserController>(context, listen: false)
          .getUserDetailsByUserId(loggedinUserId ?? 0, context)
          .then((responseMap) {
        userProfilePicture =
            userController.userModel.data?.userProfilePicture ?? '';
        userHandle = userController.userModel.data?.userHandle ?? '';
      });
      // isLoading = false;
    } catch (e) {
      log('An error occurred: $e');
    }
  }

  // STANDING CLUB SCROLL
  void _standingOnScroll() {
    if (_standingClubscrollController.position.pixels >=
            _standingClubscrollController.position.maxScrollExtent &&
        (clubController?.standingLoading == false) &&
        (clubController?.standingBookClubList?.length ?? 0) <
            (clubController?.standingClubCount ?? 0)) {
      CommonHelper.networkClose(getStandingBookClubsByUserId(true), context);
      // getStandingBookClubsByUserId(true);
    }
  }

  // // IMPROMPTU CLUB SCROLL
  void _impromptuOnScroll() {
    if (_impromClubScrollController.position.pixels >=
            _impromClubScrollController.position.maxScrollExtent &&
        (clubController?.impromptuLoading == false) &&
        (clubController?.impromptuBookClubList?.length ?? 0) <
            (clubController?.impromptuClubCount ?? 0)) {
      CommonHelper.networkClose(getImpromptuBookClubsByUserId(true), context);
      // getImpromptuBookClubsByUserId(true);
    }
  }

  Future _initializeUserIdAndFetchData() async {
    await _initializeUserId();
    await Future.wait([
      getStandingBookClubsByUserId(false),
      getImpromptuBookClubsByUserId(false),
      // getPendingInvitations(),
      getOutGoingrequest(),
    ]);
  }

  Future<void> apiCall() async {
    // await _initializeUserId();
    await Future.wait([
      getStandingBookClubsByUserId(false),
      getImpromptuBookClubsByUserId(false),
      // getPendingInvitations(),
      getOutGoingrequest(),
    ]).then((_) => setState(() {}));
  }

  Future<void> getStandingBookClubsByUserId(bool isMore) async {
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.standing, isMore)
        .then((value) async {
      if (mounted) {
        standingBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .standingBookClubList;
      }

      // Check if there are incoming requests
      bool hasNewStandingRequests = standingBookClubList?.any((element) =>
              element.incomingRequest == true &&
              element.userId == loggedinUserId) ??
          false;
      // clubController?.notifyListeners();
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateStandingClubRequests(hasNewStandingRequests);
      }
    });
  }

  Future<void> getImpromptuBookClubsByUserId(bool isMore) async {
    await Provider.of<ClubController>(context, listen: false)
        .getBookClubsByUserId(
            context, loggedinUserId ?? 0, ClubType.impromptu, isMore)
        .then((value) async {
      if (mounted) {
        impromptuBookClubList =
            Provider.of<ClubController>(context, listen: false)
                .impromptuBookClubList;
      }
      // Check if there are incoming requests
      bool hasNewImpromptuRequests = impromptuBookClubList?.any((element) =>
              element.incomingRequest == true &&
              element.userId == loggedinUserId) ??
          false;
      // clubController?.notifyListeners();

      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateImpromptuClubRequests(hasNewImpromptuRequests);
      }
    });
  }

  List<RequestManage>? pendingInvitations;
  Future<void> getPendingInvitations() async {
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            loggedinUserId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.incomingClubRequestByUserId,
            context)
        .then((value) async {
      if (mounted) {
        pendingInvitations =
            Provider.of<BookClubController>(context, listen: false)
                .incomingOutGoingRequest
                ?.data;
      }

      // Update notification status in NotificationController
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .incomingClubInvitationStatus(
                pendingInvitations?.isNotEmpty ?? false);
      }
    });
  }

  List<RequestManage>? outgoingRequestList;
  Future<void> getOutGoingrequest() async {
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(
            loggedinUserId ?? 0,
            ClubMembershipStatus.pending,
            ClubMembershipStatus.reOpened,
            ClubRequestType.outgoingClubRequestByUserId,
            context)
        .then((value) async {
      if (mounted) {
        outgoingRequestList =
            Provider.of<BookClubController>(context, listen: false)
                .incomingOutGoingRequest
                ?.data;
      }
      // checkStatus();
      // Check if there are any new outgoing requests
      bool hasNewOutgoingRequests = await checkStatus();
      // print("Outgoing Request : $hasNewOutgoingRequests");
      if (mounted) {
        await Provider.of<MessageController>(context, listen: false)
            .updateOutgoingRequests(hasNewOutgoingRequests);
      }
    });
  }

  Future<bool> checkStatus() async {
    if (outgoingRequestList?.isNotEmpty ?? false) {
      for (var element in outgoingRequestList!) {
        if (element.status == "REOPENED") {
          return true;
        }
      }
    } else {
      return false;
    }
    return false;
  }

  @override
  void dispose() {
    _impromClubScrollController.dispose();
    _standingClubscrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      width: MediaQuery.of(context).size.width,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AppConstants.bgImagePath),
          filterQuality: FilterQuality.high,
          fit: BoxFit.cover,
        ),
      ),
      child: FutureBuilder(
        future:
            _initializeUserIdAndFetchData(), //_initializeUserIdAndFetchData(),
        builder: (context, snapShot) {
          return Skeletonizer(
            effect: const SoldColorEffect(
              color: AppConstants.skeletonforgroundColor,
              lowerBound: 0.1,
              upperBound: 0.5,
            ),
            enabled: snapShot.connectionState == ConnectionState.waiting,
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: PreferredSize(
                preferredSize: const Size.fromHeight(80),
                child: Container(
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 1.5,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  ),
                  child: const AppBarWidget(
                    appBarText: 'Clubs',
                  ),
                ),
              ),
              body: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 25),
                    incomingClubInvitationsButton(context),
                    const SizedBox(height: 25),
                    outgoingClubInvitationsButton(context),
                    const SizedBox(height: 25),
                    const Skeleton.replace(
                      replacement: Divider(
                        thickness: 2,
                        color: AppConstants.skeletonBackgroundColor,
                      ),
                      child: Divider(
                        thickness: 2,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 28),
                    _buildSectionTitle(
                        context, "Standing Book Clubs", ClubType.standing),
                    (clubController?.standingBookClubList?.isEmpty ?? false)
                        ? const SizedBox(height: 10)
                        : const SizedBox.shrink(),
                    _buildStandingBookClubs(),
                    const SizedBox(height: 25),
                    const Skeleton.replace(
                      replacement: Divider(
                        thickness: 2,
                        color: AppConstants.skeletonBackgroundColor,
                      ),
                      child: Divider(
                        thickness: 2,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 28),
                    _buildSectionTitle(
                        context, "Impromptu Book Clubs", ClubType.impromptu),
                    (clubController?.impromptuBookClubList?.isEmpty ?? false)
                        ? const SizedBox(height: 10)
                        : const SizedBox.shrink(),
                    _buildImpromptuBookClubs(),
                    const SizedBox(height: 25),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget incomingClubInvitationsButton(BuildContext context) {
    return Consumer<MessageController>(
      builder: (context, notificationController, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  context.pushNamed(
                    'club-invitations',
                    queryParameters: {
                      'userId': loggedinUserId.toString(),
                    },
                  );
                },
                child: Skeleton.replace(
                  replacement: buttonSkeleton(),
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Incoming Club Invitations",
                          textAlign: TextAlign.center,
                          style: lbBold.copyWith(fontSize: 18),
                        ),
                        const SizedBox(
                          width: 15,
                        ),
                        Image.asset(
                          AppConstants.openToInvitationImagePath,
                          height: 20,
                          width: 20,
                          filterQuality: FilterQuality.high,
                          fit: BoxFit.contain,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            notificationController.isPendingInvitation
                ? Positioned(
                    top: -8,
                    right: 40,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(49),
                      child: Image.asset(
                        AppConstants.notificationImagePath,
                        height: 15,
                        width: 15,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        );
      },
    );
  }

  Widget outgoingClubInvitationsButton(BuildContext context) {
    return Consumer<MessageController>(
      builder: (context, messageController, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  context.pushNamed(
                    'club-request',
                  );
                },
                child: Skeleton.replace(
                  replacement: buttonSkeleton(),
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Outgoing Club Requests",
                          textAlign: TextAlign.center,
                          style: lbBold.copyWith(fontSize: 18),
                        ),
                        const SizedBox(
                          width: 15,
                        ),
                        Image.asset(
                          'assets/icons/Requests.png',
                          height: 25,
                          width: 25,
                          filterQuality: FilterQuality.high,
                          fit: BoxFit.contain,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            messageController.isOutgoingRequest
                ? Positioned(
                    top: -8,
                    right: 40,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(49),
                      child: Image.asset(
                        AppConstants.notificationImagePath,
                        height: 15,
                        width: 15,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        );
      },
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, String extra) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width / 1.6,
            child: Text(
              title,
              style: lbRegular.copyWith(fontSize: 20),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const Spacer(),
          NetworkAwareTap(
            onTap: () {
              context.pushNamed("NewClubScreen", extra: extra);
            },
            child: Skeleton.replace(
              replacement: Container(
                height: 30,
                width: 85,
                decoration: BoxDecoration(
                  color: AppConstants.skeletonBackgroundColor,
                  borderRadius: BorderRadius.circular(90),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      "New",
                      style: lbBold.copyWith(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Icon(
                      Icons.add_circle_outline,
                      size: 18,
                    ),
                  ],
                ),
              ),
              child: Container(
                height: 30,
                width: 85,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(90),
                  border: Border.all(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      "New",
                      style: lbBold.copyWith(
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Icon(
                      Icons.add_circle_outline,
                      size: 18,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStandingBookClubs() {
    if (clubController?.standingBookClubList != null &&
        (clubController?.standingBookClubList?.isNotEmpty ?? false)) {
      return SingleChildScrollView(
        padding: const EdgeInsets.only(left: 10, right: 20),
        scrollDirection: Axis.horizontal,
        controller: _standingClubscrollController,
        child: Consumer<ClubController>(
          builder: (context, clubController, child) {
            log("Standing Book Club Flag : ${clubController.standingLoading}");
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: List.generate(
                  (clubController.standingLoading)
                      ? (clubController.standingBookClubList?.length ?? 0) + 1
                      : (clubController.standingBookClubList?.length ?? 0),
                  (index) {
                if (index == clubController.standingBookClubList?.length &&
                    (clubController.standingLoading)) {
                  return const Padding(
                    padding: EdgeInsets.only(left: 10.0),
                    child: Center(
                      child: CircularProgressIndicator(
                        color: AppConstants.primaryColor,
                      ),
                    ),
                  );
                }
                return Skeleton.replace(
                  replacement: StandingClubMeetingInfo(
                    bookClubModel: clubController.standingBookClubList?[index],
                    loggedinUserId: loggedinUserId,
                    isLoadingSkeleton: true,
                  ),
                  child: StandingClubMeetingInfo(
                    bookClubModel: clubController.standingBookClubList?[index],
                    loggedinUserId: loggedinUserId,
                    userProfilePicture: userProfilePicture,
                    userHandle: userHandle,
                  ),
                );
              }),
            );
          },
        ),
      );
    } else {
      return Skeleton.replace(
        replacement: Container(
          padding: const EdgeInsets.only(left: 20),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: AppConstants.skeletonBackgroundColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "Join or create a standing club",
              textAlign: TextAlign.start,
            ),
          ),
        ),
        child: const NoDataWidget(
          message: "Join or create a standing club",
        ),
      );
    }
  }

  Widget _buildImpromptuBookClubs() {
    if (clubController?.impromptuBookClubList != null &&
        (clubController?.impromptuBookClubList?.isNotEmpty ?? false)) {
      return SingleChildScrollView(
        padding: const EdgeInsets.only(left: 10, right: 20),
        scrollDirection: Axis.horizontal,
        controller: _impromClubScrollController,
        child: Consumer<ClubController>(
          builder: (context, clubController, child) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: List.generate(
                (clubController.impromptuLoading)
                    ? (clubController.impromptuBookClubList?.length ?? 0) + 1
                    : (clubController.impromptuBookClubList?.length ?? 0),
                (index) {
                  // final impromptuObj =
                  //     clubController.impromptuBookClubList?[index];
                  if (index == clubController.impromptuBookClubList?.length &&
                      (clubController.impromptuLoading)) {
                    return const Padding(
                      padding: EdgeInsets.only(left: 10.0),
                      child: Center(
                        child: CircularProgressIndicator(
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    );
                  }
                  return Skeleton.replace(
                    replacement: ImpromptuClubMeetingInfo(
                      bookClubModel:
                          clubController.impromptuBookClubList?[index],
                      loggedinUserId: loggedinUserId,
                      isLoadingSkeleton: true,
                    ),
                    child: ImpromptuClubMeetingInfo(
                      bookClubModel:
                          clubController.impromptuBookClubList?[index],
                      loggedinUserId: loggedinUserId,
                      userProfilePicture: userProfilePicture,
                      userHandle: userHandle,
                    ),
                  );
                },
              ),
            );
          },
        ),
      );
    } else {
      return Skeleton.replace(
        replacement: Container(
          padding: const EdgeInsets.only(left: 20),
          margin: const EdgeInsets.symmetric(horizontal: 20),
          height: 50,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: AppConstants.skeletonBackgroundColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: const Align(
            alignment: Alignment.centerLeft,
            child: Text(
              "Join or create an impromptu club",
              textAlign: TextAlign.start,
            ),
          ),
        ),
        child: const NoDataWidget(
          message: "Join or create an impromptu club",
        ),
      );
    }
  }

  Widget clubSkeleton(
      bool isLeader, BookClubModel? bookClubItem, bool isNotification) {
    return Container(
      width: 228,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: AppConstants.skeletonBackgroundColor,
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                if (isLeader)
                  Align(
                    alignment: Alignment.centerLeft,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(49),
                      child: Image.asset(
                        AppConstants.leaderStar,
                        height: 50,
                        width: 50,
                        fit: BoxFit.cover,
                        filterQuality: FilterQuality.high,
                      ),
                    ),
                  ),
                Align(
                  alignment: Alignment.topCenter,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(49),
                    child: Image.asset(
                      (bookClubItem?.totalVacancies ?? 0) > 0
                          ? AppConstants.clubOpeningLogoImagePath
                          : AppConstants.clubOpeningZero,
                      height: 50,
                      width: 50,
                      fit: BoxFit.contain,
                      filterQuality: FilterQuality.high,
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 10,
                  child: Visibility(
                    visible: isNotification && isLeader,
                    child: Align(
                      alignment: Alignment.topRight,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(49),
                        child: Image.asset(
                          AppConstants
                              .notificationImagePath, //"assets/icons/Notification.png",
                          height: 18,
                          width: 18,
                          filterQuality: FilterQuality.high,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 25),
            Text(
              bookClubItem?.bookClubName ?? '',
              overflow: TextOverflow.ellipsis,
              style: lbBold.copyWith(fontSize: 18),
            ),
            const SizedBox(
              height: 5,
            ),
            if (bookClubItem?.bookClubType == ClubType.impromptu) ...[
              Text(
                bookClubItem?.bookAuthor ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                ),
              ),
              const SizedBox(
                height: 23,
              ),
              Text(
                bookClubItem?.clubCount ?? '',
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  fontSize: 12,
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }

  Widget buttonSkeleton() {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: AppConstants.skeletonBackgroundColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Incoming club invitations",
            textAlign: TextAlign.center,
            style: lbBold.copyWith(fontSize: 18),
          ),
          const SizedBox(
            width: 15,
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(49),
            child: Image.asset(
              AppConstants.openToInvitationImagePath,
              height: 20,
              width: 20,
              filterQuality: FilterQuality.high,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }
}
