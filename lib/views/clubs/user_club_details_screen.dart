import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/config.dart';
import '../../controller/book_club_controller.dart';
import '../../models/club_membership_model.dart';
import '../../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../../models/home_model/home_screen2_model/meeting_model.dart';
import '../../models/meeting_join_model.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/question_feedback_dialog.dart';

class UserClubDetailsScreen extends StatefulWidget {
  final int? bookClubId;
  final bool? fromChat;
  final String? userId;

  const UserClubDetailsScreen({
    super.key,
    this.bookClubId,
    this.fromChat,
    this.userId,
  });

  @override
  State<UserClubDetailsScreen> createState() => _UserClubDetailsScreenState();
}

class _UserClubDetailsScreenState extends State<UserClubDetailsScreen> {
  List<ClubMembershipModel>? memberList;
  int? loggedInUserId;
  String? userName;
  String? userHandle;
  List<RequestManage>? incomingRequestList;

  // BookClubModel? bookClubModel;
  int offSet = 0;
  int upComingMeetingLimit = 10;
  int upComingMeetingCount = 0;
  int previousMeetingLimit = 10;
  int previousMeetingCount = 0;
  bool upComingMeetingLoading = false;
  bool previousMeetingLoading = false;
  MeetingJoinModel? meetingJoinModel;
  final ScrollController _upcomingMeetingScrollController = ScrollController();
  final ScrollController _previousMeetingScrollController = ScrollController();
  bool isLoading = false;
  MessageController? messageController;

  @override
  initState() {
    messageController = Provider.of<MessageController>(context, listen: false);
    bookClubController =
        Provider.of<BookClubController>(context, listen: false);
    singleClubDetails();
    _upcomingMeetingScrollController.addListener(_upComingOnScroll);
    _previousMeetingScrollController.addListener(_previousMOnScroll);
    incomingRequestList =
        Provider.of<BookClubController>(context, listen: false)
            .incomingRequestList;
    super.initState();
  }

  Future<void> _initializeUserId() async {
    userName = await CommonHelper.getLoggedinUserName();
    userHandle = await CommonHelper.getLoggedinUserHandler();
    loggedInUserId = await CommonHelper.getLoggedInUserId();
    // if (userId != null) {
    //   loggedInUserId = userId;
    // } else {}
  }

  // UPCOMING MEETING SCROLL
  void _upComingOnScroll() {
    if (_upcomingMeetingScrollController.position.pixels >=
            _upcomingMeetingScrollController.position.maxScrollExtent &&
        !upComingMeetingLoading &&
        (upcomingMeetings?.length ?? 0) < (upComingMeetingCount)) {
      CommonHelper.networkClose(getUpcomingMeetings(true), context);
      // getUpcomingMeetings(true);
    }
  }

  // PREVIOUS MEETING SCROLL
  void _previousMOnScroll() {
    if (_previousMeetingScrollController.position.pixels >=
            _previousMeetingScrollController.position.maxScrollExtent &&
        !previousMeetingLoading &&
        (previousMeetings?.length ?? 0) < (previousMeetingCount)) {
      CommonHelper.networkClose(getPreviousMeetings(true), context);
      // getPreviousMeetings(true);
    }
  }

  Future loadData() async {
    // bookClubModel =
    //     Provider.of<BookClubController>(context, listen: false).bookClubModel;
    // await getClubDetails();
    await _initializeUserId();
    await Future.wait([
      // getClubDetails(),
      getUpcomingMeetings(false),
      getPreviousMeetings(false),
      getBookClubMembers(),
      getIncomingRequest(),
    ]);
  }

  Future<void> singleClubDetails() async {
    getClubDetails();
    if (mounted) {
      // bookClubModel =
      //     Provider.of<BookClubController>(context, listen: false).bookClubModel;
      // log("Bookclub Leader ID : ${bookClubModel?.userId}");
      // log("Bookclub Name : ${bookClubModel?.bookClubName}");
    }
  }

  List<BookClubModel>? standingBookClubList;

  Future<void> getClubDetails() async {
    log("Book Club ID : ${widget.bookClubId}");
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      null,
      widget.bookClubId,
      context,
      null,
      null,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        List<BookClubModel> bookClubList = [];
        log("Response Map : ${responseMap["data"]}");
        if (responseMap["data"].isNotEmpty) {
          bookClubList = (responseMap["data"] as List)
              .map((item) => BookClubModel.fromJson(item))
              .toList();
          standingBookClubList = bookClubList;
          if (mounted) {
            Provider.of<BookClubController>(context, listen: false)
                .updateData(standingBookClubList?[0] ?? BookClubModel());
          }
          log("Standing Book Club List : ${standingBookClubList?[0].bookClubId}");
        } else {
          standingBookClubList?.clear();
          standingBookClubList = [];
        }
      } else {}

      // await addStandingBookClubsToLocal();
      // loadMessagesFromDatabase();
    });
  }

  List<int>? memberIdsList;
  List<Map<int?, String?>> userHandles = [];
  List<Map<int?, String?>> userProfilePicture = [];
  Future<void> getBookClubMembers() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubMembers(widget.bookClubId ?? 0, context)
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        log(responseMap["data"].toString());
        memberList = (responseMap["data"] as List)
            .map((item) => ClubMembershipModel.fromJson(item))
            .toList();

        memberIdsList = memberList?.map((e) => e.userId ?? 0).toList() ?? [];
        userHandles =
            memberList?.map((e) => {e.userId: e.userHandle}).toList() ?? [];
        userProfilePicture =
            memberList?.map((e) => {e.userId: e.userProfilePicture}).toList() ??
                [];
        if (mounted) {
          Provider.of<BookClubController>(context, listen: false)
              .updateUserHandles(userHandles);
          Provider.of<BookClubController>(context, listen: false)
              .initializeIds(memberIdsList ?? []);
          Provider.of<BookClubController>(context, listen: false)
              .updateUserProfilePicture(userProfilePicture);
        }
      } else {}
    });
  }

  List<MeetingList>? upcomingMeetings;

  Future<void> getUpcomingMeetings(bool isMore) async {
    if ((upcomingMeetings?.length ?? 0) <= upComingMeetingCount || !isMore) {
      upComingMeetingLoading = true;

      if (isMore) {
        upComingMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubUpcomingMeetings(
            widget.bookClubId ?? 0, upComingMeetingLimit, offSet, context)
        .then((value) {
      upComingMeetingCount =
          Provider.of<BookClubController>(context, listen: false)
                  .upcomingMeetingModel
                  ?.count ??
              0;
      upcomingMeetings = Provider.of<BookClubController>(context, listen: false)
          .upcomingMeetingModel
          ?.data;
      Provider.of<BookClubController>(context, listen: false).notifyListeners();

      if ((upcomingMeetings?.length ?? 0) >= upComingMeetingCount) {
        upComingMeetingLoading = false;
      }
    }).whenComplete(() {
      upComingMeetingLoading = false;
    });
  }

  List<MeetingList>? previousMeetings;

  Future<void> getPreviousMeetings(bool isMore) async {
    if ((previousMeetings?.length ?? 0) <= previousMeetingCount || !isMore) {
      previousMeetingLoading = true;

      if (isMore) {
        previousMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubPreviousMeetings(
            widget.bookClubId ?? 0, previousMeetingLimit, offSet, context)
        .then((value) {
      previousMeetingCount =
          Provider.of<BookClubController>(context, listen: false)
                  .previousMeetingModel
                  ?.count ??
              0;
      previousMeetings = Provider.of<BookClubController>(context, listen: false)
          .previousMeetingModel
          ?.data;
      Provider.of<BookClubController>(context, listen: false).notifyListeners();
      if ((previousMeetings?.length ?? 0) >= previousMeetingCount) {
        previousMeetingLoading = false;
      }
    }).whenComplete(() {
      previousMeetingLoading = false;
    });
  }

  Future<void> getIncomingRequest() async {
    await Provider.of<BookClubController>(context, listen: false)
        .inComingRequest(widget.bookClubId ?? 0, ClubMembershipStatus.pending,
            '', ClubRequestType.incomingRequestByClubId, context)
        .then((_) async {
      if (mounted) {
        incomingRequestList =
            Provider.of<BookClubController>(context, listen: false)
                .incomingOutGoingRequest
                ?.data;
        await Provider.of<MessageController>(context, listen: false)
            .updateStandingClubRequests(
                incomingRequestList?.isNotEmpty ?? false);
        await Provider.of<MessageController>(context, listen: false)
            .manageIncomingRequestStatus(
                incomingRequestList?.isNotEmpty ?? false, context);

        Provider.of<BookClubController>(context, listen: false)
            .incomingRequestFunction(incomingRequestList);
      }
    });
  }

  Future<void> removeMember(int index) async {
    final payload = RequestManage(
      bookClubId: widget.bookClubId,
      bookClubMemberId: memberList?[index].bookClubMemberId,
      userId: memberList?[index].userId,
      initiatedBy: loggedInUserId,
      status: ClubMembershipStatus.left,
    );

    await Provider.of<BookClubController>(context, listen: false)
        .updateInvitation(payload, context)
        .then((value) {
      confirmDeleteFunction(index);
    });
  }

  bool isInProgress = false;
  BookClubController? bookClubController;

  Future<bool> joinMeetingFunction(int meetingId, String channelName) async {
    isLoading = true;
    setState(() {});
    bool value = await Provider.of<BookClubController>(context, listen: false)
        .joinMeeting(meetingId, loggedInUserId ?? 0, channelName);
    isLoading = false;
    setState(() {});
    return value;
  }

  @override
  void dispose() {
    _previousMeetingScrollController.dispose();
    _upcomingMeetingScrollController.dispose();
    super.dispose();
  }

  // MeetingModel? meetingModel;
  // bool flag = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: Consumer<BookClubController>(
              builder: (context, bookClubController, child) {
            return AppBar(
              backgroundColor: AppConstants.textGreenColor,
              centerTitle: true,
              title: Padding(
                padding: const EdgeInsets.only(top: 15.0),
                child: Column(
                  children: [
                    if (standingBookClubList?[0].clubCount == null) ...[
                      MarqueeList(
                        children: [
                          Text(
                            bookClubController.bookClubModel?.bookClubName ??
                                '',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ],
                      ),
                    ] else ...[
                      MarqueeList(children: [
                        Text(
                          bookClubController.bookClubModel?.bookClubName ?? '',
                          overflow: TextOverflow.ellipsis,
                          style: lbBold.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ]),
                      const SizedBox(
                        height: 2,
                      ),
                      Text(
                        bookClubController.bookClubModel?.clubCount ?? '',
                        overflow: TextOverflow.ellipsis,
                        style: lbItalic.copyWith(
                          fontSize: 14,
                        ),
                      ),
                    ],
                    // MarqueeList(children: [
                    //   Text(
                    //     bookClubController.bookClubModel?.bookClubName ?? '',
                    //     overflow: TextOverflow.ellipsis,
                    //     style: lbBold.copyWith(
                    //       fontSize: 18,
                    //     ),
                    //   ),
                    // ]),
                    // const SizedBox(
                    //   height: 2,
                    // ),
                    // Text(
                    //   bookClubController.bookClubModel?.clubCount ?? '',
                    //   overflow: TextOverflow.ellipsis,
                    //   style: lbItalic.copyWith(
                    //     fontSize: 14,
                    //   ),
                    // ),
                    const SizedBox.shrink(),
                  ],
                ),
              ),
              leading: Padding(
                padding: const EdgeInsets.only(left: 20.0, top: 10),
                child: NetworkAwareTap(
                  onTap: () {
                    widget.fromChat ?? false
                        ? context.goNamed(
                            'chat-screen',
                            queryParameters: {
                              'userId': loggedInUserId.toString(),
                              'bookClubId': widget.bookClubId.toString(),
                            },
                          )
                        : context.pop();
                  },
                  child: Image.asset(
                    width: 73,
                    height: 65,
                    "assets/icons/Back.png",
                    fit: BoxFit.contain,
                    filterQuality: FilterQuality.high,
                  ),
                ),
              ),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 20.0, top: 10),
                  child: NetworkAwareTap(
                    onTap: () {
                      showDialog(
                        context: context,
                        barrierColor: Colors.white60,
                        builder: (BuildContext context) {
                          return const QuestionFeedbackDialog();
                        },
                      );
                    },
                    child: Image.asset(
                      AppConstants.questionLogoImagePath,
                      height: 34,
                      width: 34,
                    ),
                  ),
                )
              ],
            );
            // return PreviousScreenAppBar(
            //   bookName: bookClubController.bookClubModel?.bookClubName ?? '',
            //   isSetProfile: true,
            //   impromptuCount: bookClubController.bookClubModel?.clubCount,
            //   showImpromptu: true,
            // );
          }),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: FutureBuilder(
            future: loadData(),
            builder: (context, snapShot) {
              return Skeletonizer(
                effect: const SoldColorEffect(
                  color: AppConstants.skeletonforgroundColor,
                  lowerBound: 0.1,
                  upperBound: 0.5,
                ),
                containersColor: AppConstants.skeletonBackgroundColor,
                enabled: snapShot.connectionState == ConnectionState.waiting,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: 25,
                      ),
                      bookClubController?.bookClubModel?.userId ==
                              loggedInUserId
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  NetworkAwareTap(
                                    onTap: () {
                                      context.pushNamed('club-charter', extra: {
                                        'bookClubName': bookClubController
                                            ?.bookClubModel?.bookClubName,
                                        'clubCharter': bookClubController
                                            ?.bookClubModel?.clubCharter
                                      });
                                    },
                                    child: Skeleton.replace(
                                      replacement: Container(
                                        height: 45,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                2.4,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                          color: AppConstants
                                              .skeletonBackgroundColor,
                                        ),
                                        child: Center(
                                          child: Text(
                                            "Club Charter",
                                            textAlign: TextAlign.center,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ),
                                      ),
                                      child: Container(
                                        height: 45,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                2.4,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                          color: AppConstants.textGreenColor,
                                        ),
                                        child: Center(
                                          child: Text(
                                            "Club Charter",
                                            textAlign: TextAlign.center,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Stack(
                                    clipBehavior: Clip.none,
                                    children: [
                                      NetworkAwareTap(
                                        onTap: () {
                                          Provider.of<BookClubController>(
                                                  context,
                                                  listen: false)
                                              .meeting(upcomingMeetings,
                                                  previousMeetings);
                                          context.pushNamed('clubsScreen4',
                                              queryParameters: {
                                                'bookClubId': bookClubController
                                                    ?.bookClubModel?.bookClubId
                                                    .toString(),
                                              });
                                        },
                                        child: Skeleton.replace(
                                          replacement:
                                              leaderAdminSkeleton(true),
                                          child: leaderAdminSkeleton(false),
                                        ),
                                      ),
                                      Positioned(
                                        top: -8,
                                        right: 10,
                                        child: messageController
                                                    ?.hasNewStandingClubRequest ??
                                                false
                                            ? Image.asset(
                                                AppConstants
                                                    .notificationImagePath,
                                                filterQuality:
                                                    FilterQuality.high,
                                                fit: BoxFit.cover,
                                                height: 18,
                                                width: 18,
                                              )
                                            : const SizedBox.shrink(),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          : Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: NetworkAwareTap(
                                onTap: () {
                                  context.pushNamed('club-charter', extra: {
                                    'bookClubName': bookClubController
                                        ?.bookClubModel?.bookClubName,
                                    'clubCharter': bookClubController
                                        ?.bookClubModel?.clubCharter,
                                  });
                                },
                                child: Container(
                                  height: 45,
                                  width: MediaQuery.of(context).size.width,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.textGreenColor,
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Club Charter",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Members",
                              style: lbRegular.copyWith(
                                fontSize: 20,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              (memberList?[0].totalVacancies != null &&
                                      (memberList?[0].totalVacancies ?? 0) > 0)
                                  ? "(${memberList?[0].totalVacancies} Club Openings)"
                                  : "(No Openings)",
                              style: lbRegular.copyWith(
                                fontSize: 14,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        height: 112,
                        child: ListView.builder(
                          padding: const EdgeInsets.only(left: 10, right: 20),
                          scrollDirection: Axis.horizontal,
                          itemCount: memberList?.length,
                          itemBuilder: (context, index) {
                            String memberProfilePicture =
                                memberList?[index].userProfilePicture != null
                                    ? Config.imageBaseUrl +
                                        (memberList?[index]
                                                .userProfilePicture! ??
                                            '')
                                    : AppConstants.profileLogoImagePath;
                            return NetworkAwareTap(
                              onTap: () {
                                if (loggedInUserId ==
                                    memberList?[index].userId) {
                                  // context.pushNamed('ProfileScreen');
                                } else {
                                  context
                                      .pushNamed('club-member-profile', extra: {
                                    'userId': memberList?[index].userId,
                                    'userName': memberList?[index].userName,
                                  });
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 10.0,
                                ),
                                child: Skeleton.replace(
                                  replacement: Container(
                                    width: 200,
                                    decoration: BoxDecoration(
                                      color:
                                          AppConstants.skeletonBackgroundColor,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 14.0, top: 14, bottom: 14),
                                      child: Column(
                                        children: [
                                          Stack(
                                            children: [
                                              Align(
                                                alignment: Alignment.center,
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(49),
                                                  child:
                                                      CustomCachedNetworkImage(
                                                    imageUrl:
                                                        memberProfilePicture,
                                                    width: 45,
                                                    height: 45,
                                                    errorImage: AppConstants
                                                        .profileLogoImagePath,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Text(
                                            memberList?[index].userName ?? '',
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  child: Container(
                                    width: 200,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          left: 14.0,
                                          top: 14,
                                          bottom: 14,
                                          right: 14),
                                      child: Column(
                                        children: [
                                          Stack(
                                            children: [
                                              Align(
                                                alignment: Alignment.centerLeft,
                                                child: Visibility(
                                                  visible: memberList?[index]
                                                          .userType ==
                                                      ClubMemberType.leader,
                                                  replacement: Visibility(
                                                    visible: bookClubController
                                                                ?.bookClubModel
                                                                ?.userId !=
                                                            loggedInUserId &&
                                                        loggedInUserId ==
                                                            memberList?[index]
                                                                .userId,
                                                    child: NetworkAwareTap(
                                                      onTap: () {
                                                        deleteFunction(index);
                                                      },
                                                      child: Align(
                                                        alignment:
                                                            Alignment.topLeft,
                                                        child: Text(
                                                          'Leave Club',
                                                          style:
                                                              lbItalic.copyWith(
                                                            fontSize: 10,
                                                            decoration:
                                                                TextDecoration
                                                                    .underline,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  child: Image.asset(
                                                    AppConstants.leaderStar,
                                                    height: 43,
                                                    width: 43,
                                                    fit: BoxFit.cover,
                                                    filterQuality:
                                                        FilterQuality.high,
                                                  ),
                                                ),
                                              ),
                                              // const SizedBox(
                                              //   width: 21,
                                              // ),
                                              Align(
                                                alignment: Alignment.center,
                                                child: CustomCachedNetworkImage(
                                                  imageUrl:
                                                      memberProfilePicture,
                                                  width: 45,
                                                  height: 45,
                                                  errorImage: AppConstants
                                                      .profileLogoImagePath,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Text(
                                            memberList?[index].userName ?? '',
                                            textAlign: TextAlign.center,
                                            overflow: TextOverflow.ellipsis,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      const Divider(
                        thickness: 2,
                        color: AppConstants.primaryColor,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    "Upcoming Meetings",
                                    style: lbRegular.copyWith(
                                      fontSize: 20,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : const SizedBox.shrink(),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 10,
                            )
                          : const SizedBox.shrink(),
                      upcomingMeetings?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 253,
                              child: Consumer<BookClubController>(builder:
                                  (context, bookClubController, child) {
                                return ListView.builder(
                                  physics: const ClampingScrollPhysics(),
                                  controller: _upcomingMeetingScrollController,
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 20),
                                  // shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: upComingMeetingLoading
                                      ? (upcomingMeetings?.length ?? 0) + 1
                                      : upcomingMeetings?.length,
                                  itemBuilder: (context, index) {
                                    if (index == upcomingMeetings?.length &&
                                        upComingMeetingLoading) {
                                      return const Padding(
                                        padding: EdgeInsets.only(left: 10.0),
                                        child: Center(
                                          child: CircularProgressIndicator(
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                      );
                                    }
                                    final startTime =
                                        DateTime.fromMillisecondsSinceEpoch(
                                            upcomingMeetings?[index]
                                                    .meetingStartTime ??
                                                0);

                                    // Calculate the difference between the current time and the meeting start time
                                    final difference =
                                        startTime.difference(DateTime.now());

                                    // Check if the meeting is within 30 minutes or already started (but not ended)
                                    bool showJoinButton = (difference <=
                                                const Duration(minutes: 30) &&
                                            !difference.isNegative) ||
                                        difference.isNegative;

                                    // Print the result for debugging

                                    final meetingTime =
                                        CommonHelper.getMeetingScheduleTime(
                                      upcomingMeetings?[index]
                                              .meetingStartTime ??
                                          0,
                                      upcomingMeetings?[index].meetingEndTime ??
                                          0,
                                    );
                                    String formattedDate =
                                        CommonHelper.getDayMonthYearDateFormat(
                                            upcomingMeetings?[index]
                                                .meetingDate);

                                    return Skeleton.replace(
                                      replacement: upcomingMeetingSkeleton(
                                        true,
                                        index,
                                        showJoinButton,
                                        meetingTime,
                                        formattedDate,
                                      ),
                                      child: upcomingMeetingSkeleton(
                                        false,
                                        index,
                                        showJoinButton,
                                        meetingTime,
                                        formattedDate,
                                      ),
                                    );
                                  },
                                );
                              }))
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "No upcoming meetings",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "No upcoming meetings",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                      const Divider(
                        thickness: 2,
                        color: AppConstants.primaryColor,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      previousMeetings?.isNotEmpty ?? false
                          ? Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    "Previous Meetings",
                                    style: lbRegular.copyWith(
                                      fontSize: 20,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : const SizedBox.shrink(),
                      previousMeetings?.isNotEmpty ?? false
                          ? const SizedBox(
                              height: 10,
                            )
                          : const SizedBox.shrink(),
                      previousMeetings?.isNotEmpty ?? false
                          ? SizedBox(
                              height: 188,
                              child: Consumer<BookClubController>(
                                builder: (context, bookClubController, child) {
                                  return ListView.builder(
                                    physics: const ClampingScrollPhysics(),
                                    controller:
                                        _previousMeetingScrollController,
                                    padding: const EdgeInsets.only(
                                        left: 10, right: 20),
                                    // shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: previousMeetingLoading
                                        ? (previousMeetings?.length ?? 0) + 1
                                        : previousMeetings?.length,
                                    itemBuilder: (context, index) {
                                      if (index == previousMeetings?.length &&
                                          previousMeetingLoading) {
                                        return const Padding(
                                          padding: EdgeInsets.only(left: 10.0),
                                          child: Center(
                                            child: CircularProgressIndicator(
                                              color: AppConstants.primaryColor,
                                            ),
                                          ),
                                        );
                                      }
                                      String formattedDate = CommonHelper
                                          .getDayMonthYearDateFormat(
                                              previousMeetings?[index]
                                                  .meetingDate);
                                      final meetingTime =
                                          CommonHelper.getMeetingScheduleTime(
                                              previousMeetings?[index]
                                                      .meetingStartTime ??
                                                  0,
                                              previousMeetings?[index]
                                                      .meetingEndTime ??
                                                  0);

                                      return Skeleton.replace(
                                        replacement: previousMeetingSkeleton(
                                          true,
                                          index,
                                          meetingTime,
                                          formattedDate,
                                        ),
                                        child: previousMeetingSkeleton(
                                          false,
                                          index,
                                          meetingTime,
                                          formattedDate,
                                        ),
                                      );
                                    },
                                  );
                                },
                              ),
                            )
                          : Skeleton.replace(
                              replacement: Container(
                                padding: const EdgeInsets.only(left: 20),
                                margin:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                  color: AppConstants.skeletonBackgroundColor,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Align(
                                  alignment: Alignment.centerLeft,
                                  child: Text(
                                    "No previous meetings",
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                              ),
                              child: const NoDataWidget(
                                message: "No previous meetings",
                              ),
                            ),
                      const SizedBox(
                        height: 25,
                      ),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  Widget leaderAdminSkeleton(bool skelton) {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width / 2.4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: skelton
            ? AppConstants.skeletonBackgroundColor
            : AppConstants.textGreenColor,
      ),
      child: Center(
        child: Text(
          "Leader Admin",
          textAlign: TextAlign.center,
          style: lbBold.copyWith(
            fontSize: 18,
            color: AppConstants.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget previousMeetingSkeleton(
      bool isBorder, int index, String meetingTime, String formattedDate) {
    return Container(
      padding: const EdgeInsets.all(14),
      margin: const EdgeInsets.only(left: 10),
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          MarqueeList(
            children: [
              Text(
                previousMeetings?[index].bookName ?? '',
                textAlign: TextAlign.start,
                overflow: TextOverflow.ellipsis,
                style: lbBold.copyWith(
                  fontSize: 18,
                  color: AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          MarqueeList(
            children: [
              Text(
                previousMeetings?[index].bookAuthor ?? '',
                textAlign: TextAlign.start,
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            previousMeetings?[index].partOfBookCovered ?? '',
            textAlign: TextAlign.start,
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(
            height: 15,
          ),
          Text(
            formattedDate,
            textAlign: TextAlign.start,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            meetingTime,
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            style: lbItalic.copyWith(
              fontSize: 12,
              color: AppConstants.primaryColor,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          (previousMeetings?[index].discussionQuestions?.isNotEmpty ?? false)
              ? GestureDetector(
                  onTap: () {
                    context.pushNamed(
                      'discussionScreen',
                      extra: {
                        'clubName':
                            bookClubController?.bookClubModel?.bookClubName ??
                                '',
                        'discussionQue':
                            previousMeetings?[index].discussionQuestions,
                        'userName': userName,
                      },
                    );
                  },
                  child: Text(
                    'Discussion Questions',
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 12,
                      color: AppConstants.primaryColor,
                    ),
                  ),
                )
              : Text(
                  'TBD',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 12,
                    color: AppConstants.primaryColor,
                  ),
                ),
        ],
      ),
    );
  }

  void deleteFunction(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Leave Club",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 28,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "Are you sure you would like to leave this club?",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          removeMember(index);
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Leave",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  void confirmDeleteFunction(int index) {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () async {
                    await Provider.of<BookClubController>(context,
                            listen: false)
                        .leaveClub(true);
                    if (context.mounted) {
                      context.pop();
                      context.pop();
                    }
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Leave Club",
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 28,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Text(
                    "You have left the club",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          await Provider.of<BookClubController>(context,
                                  listen: false)
                              .leaveClub(true);
                          if (context.mounted) {
                            context.pop();
                            context.pop();
                          }
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3.5,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Ok",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Widget upcomingMeetingSkeleton(
    bool isBorder,
    int index,
    bool showJoinButton,
    String meetingTime,
    String formattedDate,
  ) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  upcomingMeetings?[index].bookName ?? '',
                  // textAlign: TextAlign.start,
                  // overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  upcomingMeetings?[index].bookAuthor ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              upcomingMeetings?[index].partOfBookCovered ?? 'TBD',
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(
                fontSize: 14,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            Text(
              upcomingMeetings?[index].meetingDate != null
                  ? formattedDate
                  : 'TBD',
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              meetingTime,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
                color: AppConstants.primaryColor,
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            GestureDetector(
              onTap: () {
                context.pushNamed(
                  'discussionScreen',
                  extra: {
                    'clubName':
                        bookClubController?.bookClubModel?.bookClubName ?? '',
                    'discussionQue':
                        upcomingMeetings?[index].discussionQuestions,
                    'userName': userName,
                  },
                );
              },
              child: Text(
                'Discussion Questions',
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                style: lbItalic.copyWith(
                  decoration: TextDecoration.underline,
                  fontSize: 12,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
            const SizedBox(
              height: 15,
            ),
            (showJoinButton && meetingTime != "TBD")
                ? CustomLoaderButton(
                    buttonWidth:
                        isLoading ? 45.0 : MediaQuery.of(context).size.width,
                    buttonRadius: 30.0,
                    buttonChild: isLoading
                        ? const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(Colors.white),
                            strokeWidth: 3.0,
                          )
                        : Text(
                            'Join Now',
                            style: lbBold.copyWith(
                              fontSize: 18,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                    buttonPressed: () {
                      final meetingId = upcomingMeetings?[index].meetingId;
                      final channelName = upcomingMeetings?[index].channelName;

                      log("Meeting ID : $meetingId");
                      log("Channel Name : $channelName");

                      joinMeetingFunction(meetingId ?? 0, channelName ?? '')
                          .then(
                        (value) {
                          if (mounted) {
                            meetingJoinModel = Provider.of<BookClubController>(
                                    context,
                                    listen: false)
                                .meetingJoinModel;
                          }

                          if (value) {
                            final profilePictureUrl = (memberList
                                    ?.firstWhere(
                                        (e) => e.userId == loggedInUserId)
                                    .userProfilePicture ??
                                '');
                            if (mounted) {
                              context.pushNamed(
                                'MeetingScreen',
                                extra: {
                                  'bookName':
                                      upcomingMeetings?[index].bookName ?? '',
                                  'token': meetingJoinModel?.data,
                                  'userId': loggedInUserId,
                                  'discussionQue': upcomingMeetings?[index]
                                      .discussionQuestions,
                                  'channelName': channelName,
                                  'userName': userHandle ?? '',
                                  'profilePictureUrl':
                                      Config.imageBaseUrl + profilePictureUrl,
                                },
                              );
                            }
                          }
                        },
                      );
                    },
                  )
                // NetworkAwareTap(
                //     onTap: () async {
                //       final meetingId = upcomingMeetings?[index].meetingId;
                //       final channelName = upcomingMeetings?[index].channelName;

                //       joinMeetingFunction(meetingId ?? 0, channelName ?? '')
                //           .then((value) {
                //         meetingJoinModel = Provider.of<BookClubController>(
                //                 context,
                //                 listen: false)
                //             .meetingJoinModel;

                //         if (value) {
                //           context.pushNamed(
                //             'MeetingScreen',
                //             extra: {
                //               'bookName':
                //                   upcomingMeetings?[index].bookName ?? '',
                //               'token': meetingJoinModel?.data,
                //               'userId': loggedInUserId,
                //               'discussionQue':
                //                   upcomingMeetings?[index].discussionQuestions,
                //               'channelName':
                //                   upcomingMeetings?[index].channelName,
                //               'userName': userName,
                //               'profilePictureUrl': Config.imageBaseUrl +
                //                   (memberList?[0].userProfilePicture ?? ''),
                //             },
                //           );
                //         }
                //       });
                //     },
                //     child: Container(
                //       height: 45,
                //       width: 222,
                //       decoration: BoxDecoration(
                //         borderRadius: BorderRadius.circular(49),
                //         color: AppConstants.textGreenColor,
                //       ),
                //       child: Center(
                //         child: Text(
                //           'Join now',
                //           style: lbBold.copyWith(
                //             fontSize: 18,
                //             color: AppConstants.primaryColor,
                //           ),
                //         ),
                //       ),
                //     ),
                //   )
                : Container(
                    width: 222,
                    padding: const EdgeInsets.symmetric(
                        vertical: 10.5, horizontal: 20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.isActiveRequestColor,
                    ),
                    child: Center(
                      child: MarqueeList(
                        children: [
                          Text(
                            'Join Now (Available 30 mins before start)',
                            overflow: TextOverflow.ellipsis,
                            style: lbBold.copyWith(
                              fontSize: 16,
                              color: Colors.black38,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
