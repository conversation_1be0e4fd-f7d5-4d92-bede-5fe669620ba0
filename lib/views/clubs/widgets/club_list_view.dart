import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/constants.dart';
import '../../../models/book_club_model.dart';
import '../../../reusableWidgets/club_ui/impromptu_club_info.dart';
import '../../../reusableWidgets/club_ui/standing_club_info.dart';
import '../../../reusableWidgets/no_data_widget.dart';
import '../../../reusable_api_function/club/club_function.dart';

enum ClubListType { standing, impromptu }

class ClubListView extends StatelessWidget {
  final ClubListType type;
  final List<BookClubModel>? clubList;
  final ScrollController scrollController;
  final int? loggedInUserId;
  final String userProfilePicture;
  final String userHandle;
  final String emptyMessage;
  final VoidCallback? onScrollEnd;

  const ClubListView({
    super.key,
    required this.type,
    required this.clubList,
    required this.scrollController,
    required this.loggedInUserId,
    required this.userProfilePicture,
    required this.userHandle,
    required this.emptyMessage,
    this.onScrollEnd,
  });

  @override
  Widget build(BuildContext context) {
    if (clubList != null && clubList!.isNotEmpty) {
      return _buildClubList(context);
    } else {
      return _buildEmptyState(context);
    }
  }

  Widget _buildClubList(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(left: 10, right: 20),
      scrollDirection: Axis.horizontal,
      controller: scrollController,
      child: Consumer<ClubController>(
        builder: (context, clubController, child) {
          final isLoading = type == ClubListType.standing
              ? clubController.standingLoading
              : clubController.impromptuLoading;

          final currentList = type == ClubListType.standing
              ? clubController.standingBookClubList
              : clubController.impromptuBookClubList;

          if (type == ClubListType.standing) {
            log("Standing Book Club Flag : ${clubController.standingLoading}");
          }

          return Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: List.generate(
              isLoading
                  ? (currentList?.length ?? 0) + 1
                  : (currentList?.length ?? 0),
              (index) {
                if (index == currentList?.length && isLoading) {
                  return _buildLoadingIndicator();
                }
                return _buildClubItem(currentList?[index], index);
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildClubItem(BookClubModel? clubModel, int index) {
    return Skeleton.replace(
      replacement: _buildClubItemWidget(clubModel, isLoadingSkeleton: true),
      child: _buildClubItemWidget(clubModel, isLoadingSkeleton: false),
    );
  }

  Widget _buildClubItemWidget(BookClubModel? clubModel,
      {required bool isLoadingSkeleton}) {
    if (type == ClubListType.standing) {
      return StandingClubMeetingInfo(
        bookClubModel: clubModel,
        loggedinUserId: loggedInUserId,
        userProfilePicture: isLoadingSkeleton ? null : userProfilePicture,
        userHandle: isLoadingSkeleton ? null : userHandle,
        isLoadingSkeleton: isLoadingSkeleton,
      );
    } else {
      return ImpromptuClubMeetingInfo(
        bookClubModel: clubModel,
        loggedinUserId: loggedInUserId,
        userProfilePicture: isLoadingSkeleton ? null : userProfilePicture,
        userHandle: isLoadingSkeleton ? null : userHandle,
        isLoadingSkeleton: isLoadingSkeleton,
      );
    }
  }

  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.only(left: 10.0),
      child: Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Skeleton.replace(
      replacement: _buildEmptyStateSkeleton(context),
      child: NoDataWidget(
        message: emptyMessage,
      ),
    );
  }

  Widget _buildEmptyStateSkeleton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.symmetric(horizontal: 20),
      height: 50,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          emptyMessage,
          textAlign: TextAlign.start,
        ),
      ),
    );
  }
}

// Factory constructors for specific club types
class StandingClubListView extends ClubListView {
  const StandingClubListView({
    super.key,
    required super.clubList,
    required super.scrollController,
    required super.loggedInUserId,
    required super.userProfilePicture,
    required super.userHandle,
    super.onScrollEnd,
  }) : super(
          type: ClubListType.standing,
          emptyMessage: "Join or create a standing club",
        );
}

class ImpromptuClubListView extends ClubListView {
  const ImpromptuClubListView({
    super.key,
    required super.clubList,
    required super.scrollController,
    required super.loggedInUserId,
    required super.userProfilePicture,
    required super.userHandle,
    super.onScrollEnd,
  }) : super(
          type: ClubListType.impromptu,
          emptyMessage: "Join or create an impromptu club",
        );
}
