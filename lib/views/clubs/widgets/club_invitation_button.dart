import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../controller/message_controller.dart';
import '../../../reusableWidgets/connection_error/network_aware_tap.dart';

class ClubInvitationButton extends StatelessWidget {
  final String title;
  final String routeName;
  final String iconPath;
  final bool Function(MessageController) getNotificationStatus;
  final Map<String, String>? queryParameters;

  const ClubInvitationButton({
    super.key,
    required this.title,
    required this.routeName,
    required this.iconPath,
    required this.getNotificationStatus,
    this.queryParameters,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<MessageController>(
      builder: (context, messageController, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: NetworkAwareTap(
                onTap: () {
                  if (queryParameters != null) {
                    context.pushNamed(routeName,
                        queryParameters: queryParameters!);
                  } else {
                    context.pushNamed(routeName);
                  }
                },
                child: Skeleton.replace(
                  replacement: _buildButtonSkeleton(context),
                  child: _buildButton(context),
                ),
              ),
            ),
            if (getNotificationStatus(messageController))
              Positioned(
                top: -8,
                right: 40,
                child: _buildNotificationBadge(),
              ),
          ],
        );
      },
    );
  }

  Widget _buildButton(BuildContext context) {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: AppConstants.textGreenColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            textAlign: TextAlign.center,
            style: lbBold.copyWith(fontSize: 18),
          ),
          const SizedBox(width: 15),
          Image.asset(
            iconPath,
            height: iconPath.contains('Requests') ? 25 : 20,
            width: iconPath.contains('Requests') ? 25 : 20,
            filterQuality: FilterQuality.high,
            fit: BoxFit.contain,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonSkeleton(BuildContext context) {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: AppConstants.skeletonBackgroundColor,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            title,
            textAlign: TextAlign.center,
            style: lbBold.copyWith(fontSize: 18),
          ),
          const SizedBox(width: 15),
          ClipRRect(
            borderRadius: BorderRadius.circular(49),
            child: Image.asset(
              iconPath,
              height: iconPath.contains('Requests') ? 25 : 20,
              width: iconPath.contains('Requests') ? 25 : 20,
              filterQuality: FilterQuality.high,
              fit: BoxFit.contain,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationBadge() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(49),
      child: Image.asset(
        AppConstants.notificationImagePath,
        height: 15,
        width: 15,
        filterQuality: FilterQuality.high,
        fit: BoxFit.cover,
      ),
    );
  }
}

// Factory constructors for common button types
class IncomingClubInvitationButton extends StatelessWidget {
  final int? loggedInUserId;

  const IncomingClubInvitationButton({
    super.key,
    required this.loggedInUserId,
  });

  @override
  Widget build(BuildContext context) {
    return ClubInvitationButton(
      title: "Incoming Club Invitations",
      routeName: 'club-invitations',
      iconPath: AppConstants.openToInvitationImagePath,
      getNotificationStatus: _getIncomingNotificationStatus,
      queryParameters: {
        'userId': loggedInUserId?.toString() ?? '0',
      },
    );
  }

  static bool _getIncomingNotificationStatus(MessageController controller) {
    return controller.isPendingInvitation;
  }
}

class OutgoingClubRequestButton extends ClubInvitationButton {
  const OutgoingClubRequestButton({super.key})
      : super(
          title: "Outgoing Club Requests",
          routeName: 'club-request',
          iconPath: 'assets/icons/Requests.png',
          getNotificationStatus: _getOutgoingNotificationStatus,
        );

  static bool _getOutgoingNotificationStatus(MessageController controller) {
    return controller.isOutgoingRequest;
  }
}
