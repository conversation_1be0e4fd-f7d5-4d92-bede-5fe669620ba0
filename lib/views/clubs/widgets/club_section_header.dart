import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../constants/constants.dart';
import '../../../constants/text_style.dart';
import '../../../reusableWidgets/connection_error/network_aware_tap.dart';

class ClubSectionHeader extends StatelessWidget {
  final String title;
  final String clubType;

  const ClubSectionHeader({
    super.key,
    required this.title,
    required this.clubType,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: lbRegular.copyWith(fontSize: 20),
              overflow: TextOverflow.ellipsis,
              semanticsLabel: '$title section',
            ),
          ),
          const SizedBox(width: 16),
          _buildNewButton(context),
        ],
      ),
    );
  }

  Widget _buildNewButton(BuildContext context) {
    return NetworkAwareTap(
      onTap: () {
        context.pushNamed("NewClubScreen", extra: clubType);
      },
      child: Skeleton.replace(
        replacement: _buildNewButtonSkeleton(),
        child: _buildNewButtonContent(),
      ),
    );
  }

  Widget _buildNewButtonContent() {
    return Container(
      height: 30,
      width: 85,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(90),
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text(
            "New",
            style: lbBold.copyWith(fontSize: 14),
            overflow: TextOverflow.ellipsis,
          ),
          const Icon(
            Icons.add_circle_outline,
            size: 18,
            semanticLabel: 'Add new club',
          ),
        ],
      ),
    );
  }

  Widget _buildNewButtonSkeleton() {
    return Container(
      height: 30,
      width: 85,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(90),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text(
            "New",
            style: lbBold.copyWith(fontSize: 14),
            overflow: TextOverflow.ellipsis,
          ),
          const Icon(
            Icons.add_circle_outline,
            size: 18,
          ),
        ],
      ),
    );
  }
}
