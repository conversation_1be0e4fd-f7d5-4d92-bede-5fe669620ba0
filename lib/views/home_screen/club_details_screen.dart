import 'dart:developer';

import 'package:eljunto/constants/common_helper.dart';
import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/generic_messages.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_club_controller.dart';
import 'package:eljunto/models/book_club_model.dart';
import 'package:eljunto/models/home_model/home_screen2_model/meeting_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/no_data_widget.dart';
import 'package:eljunto/reusableWidgets/previous_screen_appbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../models/home_model/home_screen2_model/member_model.dart';
import '../../reusableWidgets/club_join_popup.dart';
import '../../reusableWidgets/customDialouge_with_message.dart';

class ClubDetails extends StatefulWidget {
  final int bookClubId;
  final String? bookClubName;
  final String? impromptuCount;

  const ClubDetails({
    super.key,
    required this.bookClubId,
    this.bookClubName,
    this.impromptuCount,
  });

  @override
  State<ClubDetails> createState() => _ClubDetailsState();
}

class _ClubDetailsState extends State<ClubDetails> {
  bool isLoading = true;
  bool showValidationMessage = false;

  // List<MeetingModel> upcomingMeetings = [];
  // List<MeetingModel> previousMeetings = [];
  List<MemberModel> memberList = [
    MemberModel(
      userId: 1,
      userName: 'John Doe',
      userProfilePicture: '',
    ),
    MemberModel(
      userId: 1,
      userName: 'John Doe',
      userProfilePicture: '',
    ),
  ];
  BookClubModel? bookClubDetails;
  int offSet = 0;
  int limit = 10;
  int upComingMeetingLimit = 10;
  int upComingMeetingCount = 0;
  int previousMeetingLimit = 10;
  int previousMeetingCount = 0;
  bool upComingMeetingLoading = false;
  bool previousMeetingLoading = false;
  final ScrollController _upcomingMeetingScrollController = ScrollController();
  final ScrollController _previousMeetingScrollController = ScrollController();

  @override
  void initState() {
    _upcomingMeetingScrollController.addListener(_upComingOnScroll);
    _previousMeetingScrollController.addListener(_previousMOnScroll);
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loadData(); // Ensures loadData is triggered after build
    });
  }

  // UPCOMING MEETING SCROLL
  void _upComingOnScroll() {
    if (_upcomingMeetingScrollController.position.pixels >=
            _upcomingMeetingScrollController.position.maxScrollExtent &&
        !upComingMeetingLoading &&
        (upcomingMeetings?.length ?? 0) < (upComingMeetingCount)) {
      CommonHelper.networkClose(getUpcomingMeetings(true), context);
      // getUpcomingMeetings(true);
    }
  }

  // PREVIOUS MEETING SCROLL
  void _previousMOnScroll() {
    if (_previousMeetingScrollController.position.pixels >=
            _previousMeetingScrollController.position.maxScrollExtent &&
        !previousMeetingLoading &&
        (previousMeetings?.length ?? 0) < (previousMeetingCount)) {
      CommonHelper.networkClose(getPreviousMeetings(true), context);
      // getPreviousMeetings(true);
    }
  }

  Future<void> getBookClubDetails() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubs(
      '',
      null,
      widget.bookClubId,
      context,
      offSet,
      limit,
    )
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        log(responseMap["data"].toString());
        // setState(() {
        List<dynamic> data = responseMap["data"];
        if (data.isNotEmpty) {
          bookClubDetails = BookClubModel.fromJson(data[0]);
        }
        // });
      } else {}
    });
  }

  Future<void> getBookClubMembers() async {
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubMembers(widget.bookClubId, context)
        .then((responseMap) async {
      if (responseMap["statusCode"] == 200) {
        memberList.cast();
        log(responseMap["data"].toString());
        // setState(() {
        memberList = (responseMap["data"] as List)
            .map((item) => MemberModel.fromJson(item))
            .toList();
        //memberList = responseMap["data"];
        // });
      } else {}
    });
  }

  List<MeetingList>? upcomingMeetings;

  Future<void> getUpcomingMeetings(bool isMore) async {
    if ((upcomingMeetings?.length ?? 0) <= upComingMeetingCount || !isMore) {
      upComingMeetingLoading = true;

      if (isMore) {
        upComingMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubUpcomingMeetings(
            widget.bookClubId, upComingMeetingLimit, offSet, context)
        .then((value) {
      if (mounted) {
        upComingMeetingCount =
            Provider.of<BookClubController>(context, listen: false)
                    .upcomingMeetingModel
                    ?.count ??
                0;
        upcomingMeetings =
            Provider.of<BookClubController>(context, listen: false)
                .upcomingMeetingModel
                ?.data;
      }
      // Provider.of<BookClubController>(context, listen: false).notifyListeners();
      // print("Meeting Count : $upComingMeetingCount");
      if ((upcomingMeetings?.length ?? 0) >= upComingMeetingCount) {
        upComingMeetingLoading = false;
      }
    }).whenComplete(() {
      upComingMeetingLoading = false;
    });
    // print("Upcoming List : $upcomingMeetings");
  }

  List<MeetingList>? previousMeetings;

  Future<void> getPreviousMeetings(bool isMore) async {
    if ((previousMeetings?.length ?? 0) <= previousMeetingCount || !isMore) {
      previousMeetingLoading = true;

      if (isMore) {
        previousMeetingLimit += 10;
      }
    }
    await Provider.of<BookClubController>(context, listen: false)
        .getBookClubPreviousMeetings(
            widget.bookClubId, previousMeetingLimit, offSet, context)
        .then((value) {
      if (mounted) {
        previousMeetingCount =
            Provider.of<BookClubController>(context, listen: false)
                    .previousMeetingModel
                    ?.count ??
                0;
        previousMeetings =
            Provider.of<BookClubController>(context, listen: false)
                .previousMeetingModel
                ?.data;
      }
      if ((previousMeetings?.length ?? 0) >= previousMeetingCount) {
        previousMeetingLoading = false;
      }
      // print("Previous Meeting Count : $previousMeetingCount");
      // print("Previous List : $previousMeetings");
    }).whenComplete(() {
      previousMeetingLoading = false;
    });
  }

  Future<void> loadData() async {
    await getBookClubDetails();
    await Future.wait([
      getBookClubMembers(),
      getUpcomingMeetings(false),
      getPreviousMeetings(false),
    ]);
  }

  @override
  void dispose() {
    _previousMeetingScrollController.dispose();
    _upcomingMeetingScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.bookClubName,
            isSetProfile: true,
            impromptuCount: widget.impromptuCount,
            // showImpromptu: widget.isImpromptuClub,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
          future: loadData(),
          builder: (context, snapShot) {
            return Skeletonizer(
              effect: const SoldColorEffect(
                color: AppConstants.skeletonforgroundColor,
                lowerBound: 0.1,
                upperBound: 0.5,
              ),
              containersColor: AppConstants.skeletonBackgroundColor,
              enabled: snapShot.connectionState == ConnectionState.waiting,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () {
                              context.pushNamed('club-charter', extra: {
                                'bookClubName': bookClubDetails?.bookClubName,
                                'clubCharter': bookClubDetails?.clubCharter
                              });
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 2.4,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Club Charter",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          if (bookClubDetails?.totalVacancies != null &&
                              (bookClubDetails?.totalVacancies ?? 0) > 0) ...[
                            NetworkAwareTap(
                              onTap: () {
                                showJoinClubPopup(
                                    bookClubDetails?.bookClubId ?? 0,
                                    bookClubDetails?.memberReqPrompt ?? '');
                              },
                              child: Skeleton.replace(
                                replacement: Container(
                                  height: 45,
                                  width:
                                      MediaQuery.of(context).size.width / 2.4,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.skeletonBackgroundColor,
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Join",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                                child: Container(
                                  height: 45,
                                  width:
                                      MediaQuery.of(context).size.width / 2.4,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(49),
                                    color: AppConstants.textGreenColor,
                                  ),
                                  child: Center(
                                    child: Text(
                                      "Join",
                                      textAlign: TextAlign.center,
                                      style: lbBold.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ] else ...[
                            NetworkAwareTap(
                              onTap: () {
                                // showJoinClubPopup();
                              },
                              child: Container(
                                height: 45,
                                width: MediaQuery.of(context).size.width / 2.5,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(49),
                                  color: Colors.transparent,
                                  border: Border.all(
                                    color: const Color.fromRGBO(45, 45, 45, 1),
                                  ),
                                ),
                                child: Center(
                                  child: Text(
                                    "No openings",
                                    textAlign: TextAlign.center,
                                    style: lbBold.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ]
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Members",
                            textAlign: TextAlign.center,
                            style: lbRegular.copyWith(
                              fontSize: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      height: 112,
                      child: ListView.builder(
                        padding: const EdgeInsets.only(left: 10, right: 20),
                        scrollDirection: Axis.horizontal,
                        itemCount: memberList.length,
                        itemBuilder: (context, index) {
                          String memberProfilePicture =
                              memberList[index].userProfilePicture != null
                                  ? Config.imageBaseUrl +
                                      memberList[index].userProfilePicture!
                                  : AppConstants.profileLogoImagePath;
                          return NetworkAwareTap(
                            onTap: () {
                              context.pushNamed(
                                'club-member-profile',
                                extra: {
                                  'userId': memberList[index].userId,
                                  'userName': memberList[index].userName
                                },
                              );
                            },
                            child: Skeleton.replace(
                              replacement: memberSkeleton(
                                  true, index, memberProfilePicture),
                              child: memberSkeleton(
                                  false, index, memberProfilePicture),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    const Divider(
                      thickness: 2,
                      color: AppConstants.primaryColor,
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    if (upcomingMeetings?.isEmpty ?? false) ...[
                      Skeleton.replace(
                        replacement: Container(
                          padding: const EdgeInsets.only(left: 20),
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonBackgroundColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "No upcoming meetings",
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ),
                        child: const NoDataWidget(
                          message: "No upcoming meetings",
                        ),
                      ),
                    ] else ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Upcoming Meetings",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        height: 167,
                        child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                            return ListView.builder(
                              controller: _upcomingMeetingScrollController,
                              padding:
                                  const EdgeInsets.only(left: 10, right: 20),
                              scrollDirection: Axis.horizontal,
                              itemCount: upComingMeetingLoading
                                  ? (upcomingMeetings?.length ?? 0) + 1
                                  : upcomingMeetings?.length,
                              itemBuilder: (context, index) {
                                if (index == upcomingMeetings?.length &&
                                    upComingMeetingLoading) {
                                  return const Padding(
                                    padding: EdgeInsets.only(left: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }
                                String formattedDate =
                                    CommonHelper.getDayMonthYearDateFormat(
                                        upcomingMeetings?[index].meetingDate);
                                final time =
                                    CommonHelper.getMeetingScheduleTime(
                                        upcomingMeetings?[index]
                                                .meetingStartTime ??
                                            0,
                                        upcomingMeetings?[index]
                                                .meetingEndTime ??
                                            0);
                                return Skeleton.replace(
                                  replacement: upComingMeetingSkeleton(
                                    true,
                                    index,
                                    formattedDate,
                                    time,
                                  ),
                                  child: upComingMeetingSkeleton(
                                    false,
                                    index,
                                    formattedDate,
                                    time,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                    const SizedBox(
                      height: 25,
                    ),
                    const Divider(
                      thickness: 2,
                      color: AppConstants.primaryColor,
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    if (previousMeetings?.isEmpty ?? false) ...[
                      Skeleton.replace(
                        replacement: Container(
                          padding: const EdgeInsets.only(left: 20),
                          margin: const EdgeInsets.symmetric(horizontal: 20),
                          height: 50,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            color: AppConstants.skeletonBackgroundColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Align(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "No previous meetings",
                              textAlign: TextAlign.start,
                            ),
                          ),
                        ),
                        child: const NoDataWidget(
                          message: "No previous meetings",
                        ),
                      ),
                    ] else ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Previous Meetings",
                              textAlign: TextAlign.center,
                              style: lbRegular.copyWith(
                                fontSize: 20,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      SizedBox(
                        height: 170,
                        child: Consumer<BookClubController>(
                          builder: (context, bookClubController, child) {
                            return ListView.builder(
                              controller: _previousMeetingScrollController,
                              padding: const EdgeInsets.only(
                                  left: 10, right: 20, bottom: 25),
                              scrollDirection: Axis.horizontal,
                              itemCount: previousMeetingLoading
                                  ? (previousMeetings?.length ?? 0) + 1
                                  : previousMeetings?.length,
                              itemBuilder: (context, index) {
                                if (index == previousMeetings?.length &&
                                    previousMeetingLoading) {
                                  return const Padding(
                                    padding: EdgeInsets.only(left: 10.0),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        color: AppConstants.primaryColor,
                                      ),
                                    ),
                                  );
                                }
                                String formattedDate =
                                    CommonHelper.getDayMonthYearDateFormat(
                                        previousMeetings?[index].meetingDate);
                                return Skeleton.replace(
                                  replacement: previousMeetingSkeleton(
                                    true,
                                    index,
                                    formattedDate,
                                  ),
                                  child: previousMeetingSkeleton(
                                    false,
                                    index,
                                    formattedDate,
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                    const SizedBox(
                      height: 25,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget memberSkeleton(bool isBorder, int index, String memberProfilePicture) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          left: 14.0,
          top: 14,
          bottom: 14,
          right: 14,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              // mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (memberList[index].userType?.toUpperCase() ==
                    ClubMemberType.leader.toUpperCase()) ...[
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Visibility(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: Image.asset(
                          AppConstants.leaderStar,
                          height: 43,
                          width: 43,
                          fit: BoxFit.cover,
                          filterQuality: FilterQuality.high,
                        ),
                      ),
                    ),
                  ),
                ],
                Align(
                  alignment: Alignment.center,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(50),
                    child: CustomCachedNetworkImage(
                      imageUrl: memberProfilePicture,
                      width: 45,
                      height: 45,
                      errorImage: AppConstants.profileLogoImagePath,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              memberList[index].userName ?? '',
              textAlign: TextAlign.start,
              overflow: TextOverflow.ellipsis,
              style: lbBold.copyWith(
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget upComingMeetingSkeleton(
      bool isBorder, int index, String formattedDate, String time) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  upcomingMeetings?[index].bookName ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  upcomingMeetings?[index].bookAuthor ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  upcomingMeetings?[index].partOfBookCovered ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 21,
            ),
            Text(
              formattedDate,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            Text(
              time,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget previousMeetingSkeleton(
      bool isBorder, int index, String formattedDate) {
    return Container(
      margin: const EdgeInsets.only(left: 10),
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        border: Border.all(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  previousMeetings?[index].bookName ?? '',
                  textAlign: TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  previousMeetings?[index].bookAuthor ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 5,
            ),
            MarqueeList(
              children: [
                Text(
                  previousMeetings?[index].partOfBookCovered ?? '',
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 21,
            ),
            Text(
              formattedDate,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: lbItalic.copyWith(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void handleSubmitJoinRequest(String requestMessage, Function setState) {
    if (requestMessage.isEmpty) {
      setState(() {
        showValidationMessage = true;
      });
    } else {
      clearValidationMessage();
      context.pop();
      confirmJoinRequest(requestMessage);
    }
  }

  clearValidationMessage() {
    setState(() {
      showValidationMessage = false;
    });
  }

  void showJoinClubPopup(int bookClubId, String requestPrompt) {
    clearValidationMessage();
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return ClubJoinPopup(
              bookClubId: bookClubId,
              memberRequestPrompt: requestPrompt,
            );
          },
        );
      },
    );
  }

  Future<void> confirmJoinRequest(String requestMessage) async {
    int? loggedInUserId = await CommonHelper.getLoggedInUserId();
    String? userName = await CommonHelper.getLoggedinUserName();
    String responseMessage = '';
    bool showDoneImg = false;
    final Map<String, dynamic> payload = {
      "bookClubId": widget.bookClubId,
      "userId": loggedInUserId,
      "userType": ClubMemberType.member,
      "initiatedBy": loggedInUserId,
      "requestMessage": requestMessage
    };

    try {
      final responseMap =
          await Provider.of<BookClubController>(context, listen: false)
              .addMember(payload, context);

      if (responseMap["statusCode"] == 200) {
        responseMessage = GenericMessages.joinRequestSuccess;
        showDoneImg = true;
      } else {
        responseMessage = responseMap['error'];
        showDoneImg = false;
      }
    } catch (e) {
      responseMessage = "Failed to submit request: $e";
    }
    if (showDoneImg) {
      showRequestFunction();
    } else {
      if (mounted) {
        showDialog(
          barrierColor: Colors.white60,
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return CustomDialog(
              title: "Join: ${userName ?? ''}",
              message: responseMessage,
              showDoneImage: showDoneImg,
            );
          },
        );
      }
    }
  }

  void requestFunction() {
    showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                Container(
                  alignment: Alignment.centerRight,
                  padding: const EdgeInsets.only(top: 10),
                  child: Image.asset(
                    AppConstants.closePopupImagePath,
                    height: 30,
                    width: 30,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30, right: 20),
                  child: Image.asset(
                    "assets/icons/Done.png",
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                    height: 79,
                    width: 79,
                  ),
                ),
                const SizedBox(height: 25),
                Padding(
                  padding: const EdgeInsets.only(left: 30, right: 20),
                  child: Text(
                    "Your request to join has been submitted to the club leader",
                    textAlign: TextAlign.center,
                    style: lbRegular.copyWith(
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        context.pop();
                      },
                      child: Container(
                        height: 45,
                        width: MediaQuery.of(context).size.width / 3.5,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(49),
                          color: AppConstants.textGreenColor,
                        ),
                        child: Center(
                          child: Text(
                            "Ok",
                            textAlign: TextAlign.center,
                            style: lbBold.copyWith(
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Future<void> showRequestFunction() async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Column(
                    children: [
                      Image.asset(
                        AppConstants.requestDoneImg,
                        filterQuality: FilterQuality.high,
                        fit: BoxFit.cover,
                        height: 79,
                        width: 79,
                      ),
                      const SizedBox(
                        height: 25,
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          "Your request to join has been submitted to the club leader",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                NetworkAwareTap(
                  onTap: () async {
                    // final bookId = currentbookCaseList?[index].bookId;
                    // await deleteBook(bookId).then(
                    //   (value) {},
                    // );
                    // setState(() {});
                    if (context.mounted) {
                      context.pop();
                    }
                  },
                  child: Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width / 3.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(49),
                      color: AppConstants.textGreenColor,
                    ),
                    child: Center(
                      child: Text(
                        "Ok",
                        textAlign: TextAlign.center,
                        style: lbBold.copyWith(
                          fontSize: 18,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }
}
