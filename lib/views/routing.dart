import 'dart:developer';

import 'package:eljunto/controller/connectivity_controller.dart';
import 'package:eljunto/controller/message_controller.dart';
import 'package:eljunto/controller/user_credential_controller.dart';
import 'package:eljunto/models/user_model.dart';
import 'package:eljunto/reusableWidgets/bottomnavigationbar_.dart';
import 'package:eljunto/reusableWidgets/connection_error/connection_lost_screen.dart';
import 'package:eljunto/reusableWidgets/connection_error/server_unavailable_screen.dart';
import 'package:eljunto/reusableWidgets/transition_widget.dart';
import 'package:eljunto/services/setup_locator.dart';
import 'package:eljunto/views/clubs/club_invitations.dart';
import 'package:eljunto/views/clubs/clubs_home_screen.dart';
import 'package:eljunto/views/clubs/discussion_question_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/add_edit_meeting_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/charter_member_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_incoming_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_meetings_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_memeber_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/manage_outgoing_request_screen.dart';
import 'package:eljunto/views/clubs/leader_admin_option/new_book_club_screen.dart';
import 'package:eljunto/views/clubs/outgoing_club_request_screen.dart';
import 'package:eljunto/views/clubs/user_club_details_screen.dart';
import 'package:eljunto/views/home_screen/book_review_screen.dart';
import 'package:eljunto/views/home_screen/club_charter_screen.dart';
import 'package:eljunto/views/home_screen/club_details_screen.dart';
import 'package:eljunto/views/home_screen/club_member_bookcase_screen.dart';
import 'package:eljunto/views/home_screen/club_member_profile_screen.dart';
import 'package:eljunto/views/home_screen/home_screen.dart';
import 'package:eljunto/views/home_screen/home_screen_6.dart';
import 'package:eljunto/views/login/name_handle_screen.dart';
import 'package:eljunto/views/login/otp_screen.dart';
import 'package:eljunto/views/meeting/meeting_screen.dart';
import 'package:eljunto/views/messages/chat_screen.dart';
import 'package:eljunto/views/messages/messages_screen.dart';
import 'package:eljunto/views/profile/edit_bookcase_screen.dart';
import 'package:eljunto/views/profile/edit_current_reading_screen.dart';
import 'package:eljunto/views/profile/edit_profile_screen.dart';
import 'package:eljunto/views/profile/profile_screen.dart';
import 'package:eljunto/views/profile/to_be_read_screen.dart';
import 'package:eljunto/views/search/search_screen.dart';
import 'package:eljunto/views/search/show_club_screen.dart';
import 'package:eljunto/views/settings/block_list.dart';
import 'package:eljunto/views/settings/delete_account.dart';
import 'package:eljunto/views/settings/manage_email.dart';
import 'package:eljunto/views/settings/manage_subscription.dart';
import 'package:eljunto/views/settings/notification_settings.dart';
import 'package:eljunto/views/settings/privacy_policy.dart';
import 'package:eljunto/views/settings/settings_screen.dart';
import 'package:eljunto/views/settings/terms_of_service.dart';
import 'package:eljunto/views/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../constants/constants.dart';
import '../controller/subscription_controller.dart';
import 'clubs/fellow_reader_screen.dart';
import 'clubs/leader_admin_option_screen.dart';
import 'clubs/new_club_opening_screen.dart';
import 'login/login_screen.dart';
import 'login/profile_setup_finalStep.dart';
import 'login/set_password.dart';
import 'login/sign_up.dart';
import 'login/subscription_screen.dart';
import 'settings/change_password.dart';

class AppRouter {
  AppRouter._();

  static bool appInitialized = false;
  static String? lastClickedDeepLink;

  /// Helper function to store pending route in SharedPreferences
  static Future<void> storePendingRoute(String route) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('pendingDeepLink', route);
  }

  /// Get pending route WITHOUT clearing - used to check if one exists
  static Future<String?> getPendingRouteWithoutClearing() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingRoute = prefs.getString('pendingDeepLink');
    log("Retrieved pending route (without clearing): $pendingRoute");
    return pendingRoute;
  }

  /// Helper function to retrieve and clear pending route
  static Future<String?> getPendingRouteAndClear() async {
    final prefs = await SharedPreferences.getInstance();
    final pendingRoute = prefs.getString('pendingDeepLink');
    if (pendingRoute != null) {
      await prefs.remove('pendingDeepLink');
      log("Cleared pending deep link after retrieving: $pendingRoute");
    }
    return pendingRoute;
  }

  // Explicitly clear pending route
  static Future<void> clearPendingRoute() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('pendingDeepLink');
    log("Explicitly cleared pending route");
  }

  static final GlobalKey<NavigatorState> rootNavigatorKeys =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorHome =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorProfile =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorMessage =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorClubs =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorSearch =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorSettings =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _rootNavigatorNoNetwork =
      GlobalKey<NavigatorState>();

  bool isProfileSet() {
    return false;
  }

  static int? loginUserId;

  static Future<String?> redirect(
      BuildContext context, GoRouterState state) async {
    log("--- Redirect START for ${state.uri} (App Initialized: $appInitialized) ---");

    final userCredentialController =
        Provider.of<UserCredentialController>(context, listen: false);
    final subscriptionController =
        Provider.of<SubscriptionController>(context, listen: false);
    final messageController =
        Provider.of<MessageController>(context, listen: false);
    final connectivityProvider = locator<ConnectivityProvider>();

    final requestedPath = state.uri.toString();
    // CHECK QUERY PARAMETERS
    final String deepLinkQuery = state.uri.hasQuery ? state.uri.query : "";

    // APP INITIALIZATION CHECK
    if (!appInitialized) {
      if (requestedPath == '/') {
        return null;
      } else {
        AppRouter.storePendingRoute(requestedPath);
        return '/';
      }
    }

    // CHECK CONNECTIVITY
    if (connectivityProvider.status == InternetStatus.disconnected) {
      log("Redirect: No internet. Allowing navigation (screen should handle).");
      return null;
    }

    // CHECK FOR EXTERNAL URLS
    if ((requestedPath == AppConstants.privacyPolicyUrl.toString() ||
            requestedPath == AppConstants.termsAndConditionUrl.toString()) ||
        requestedPath == 'https://eljunto.com/') {
      if (await canLaunchUrl(Uri.parse(requestedPath))) {
        await launchUrl(Uri.parse(requestedPath));
      }
      return '/Home';
    }

    // FETCH LOGIN USERID
    int? loggedInUserId = userCredentialController.userId;
    loggedInUserId ??= await userCredentialController.getUserId();

    // CHECK PENDING ROUTE IS AVAILABLE
    if (requestedPath == '/') {
      final pendingRouteFromStart = await AppRouter.getPendingRouteAndClear();
      if (pendingRouteFromStart != null) {
        return pendingRouteFromStart;
      }
    }

    // DEEPLINK USERID CHECK VALIDATION
    if (deepLinkQuery.isNotEmpty && deepLinkQuery.contains('userId=')) {
      try {
        final queryParams = Uri.parse("?$deepLinkQuery").queryParameters;
        final String? userIdFromDeepLink = queryParams['userId'];

        if (userIdFromDeepLink != null && userIdFromDeepLink.isNotEmpty) {
          final int? extractedDeepLinkUserId = int.tryParse(userIdFromDeepLink);

          if (extractedDeepLinkUserId == null) {
            if (context.mounted) {
              await messageController.isInvalidDeeplink(true);
            }
            return '/Home';
          }

          // VALIDATION LOGIC IF USER ID IS DIFFERENT
          if (loggedInUserId != null &&
              extractedDeepLinkUserId != loggedInUserId) {
            if (context.mounted) {
              await messageController.isInvalidDeeplink(true);
            }
            return '/Home';
          }
        } else {
          log("Deeplink 'userId' is null : '$deepLinkQuery'");
        }
      } catch (e) {
        log(e.toString());
        if (context.mounted) {
          await messageController.isInvalidDeeplink(true);
        }
        return '/Home';
      }
    } else {
      log("No 'userId=' found in deepLinkQuery : '$deepLinkQuery'");
    }

    // AUTHENTICATION AND SUBSCRIPTION CHECKS
    final isLoggedIn = await userCredentialController.isUserLoggedIn();

    if (loggedInUserId == null && userCredentialController.userId != null) {
      loggedInUserId = userCredentialController.userId;
    }

    bool isSubscriptionActive = false;
    if (isLoggedIn) {
      log("User is logged in. Checking subscription...");
      if (!context.mounted) {
        log("Context unmounted before subscription check. Aborting redirect.");
        return null;
      }
      try {
        if (subscriptionController.verifySubscription) {
          await subscriptionController.isActiveSubscription();
        }
        final status =
            subscriptionController.verifySubscriptionModel?.data?.usubStatus;
        isSubscriptionActive = status == 'ACTIVE' || status == 'CANCELLED';
      } catch (e) {
        isSubscriptionActive = false;
      }
    } else {
      log("User not logged in");
    }

    // ROUTE ACCESS CONTROL
    const Set<String> publicRoutes = {
      '/',
      '/login',
      '/sign-up',
      '/otp',
      '/set-password',
      '/set-name-handle',
      '/profilesetup',
      '/privacy-policy',
      '/terms-of-service',
      '/trial-delete-account',
    };

    final bool isPublicRoute = publicRoutes.contains(state.matchedLocation) ||
        publicRoutes.contains(requestedPath);

    if (state.matchedLocation == '/subscription') {
      if (!isLoggedIn) {
        AppRouter.storePendingRoute(state.uri.toString());
        return '/login';
      }
      log("Accessing /subscription page. Allowing.");
      return null;
    }

    // IF USER IS NOT LOGGED IN
    if (!isLoggedIn && !isPublicRoute) {
      AppRouter.storePendingRoute(state.uri.toString());
      return '/login';
    }

    // IF USER IS LOGGED IN BUT NO ACTIVE SUBSCRIPTION
    if (isLoggedIn && !isSubscriptionActive && !isPublicRoute) {
      AppRouter.storePendingRoute(state.uri.toString());
      return '/subscription';
    }

    log("No redirect needed for $requestedPath. Allowing navigation.");
    return null;
  }

  static final GoRouter router = GoRouter(
    initialLocation: '/',
    navigatorKey: rootNavigatorKeys,
    debugLogDiagnostics: true,
    redirect: (context, state) async => await redirect(context, state),
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        name: 'SplashScreen',
        builder: (context, state) {
          return const SplashScreen();
        },
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        pageBuilder: (context, state) {
          return CustomTransitionPage(
            key: state.pageKey,
            child: const LoginPage(),
            transitionDuration: const Duration(milliseconds: 950),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              const begin = Offset(0.0, 1.0);
              const end = Offset.zero;
              const curve = Curves.easeInOut;
              final tween = Tween(begin: begin, end: end).chain(
                CurveTween(curve: curve),
              );
              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          );
        },
        builder: (context, state) {
          return const LoginPage();
        },
      ),
      GoRoute(
        path: "/MeetingScreen",
        name: "MeetingScreen",
        pageBuilder: (context, state) {
          final extras = state.extra as Map;
          return TransitionPageWidget.navigateTransitionPage(
            child: MeetingScreen(
              key: state.pageKey,
              bookName: extras['bookName'],
              token: extras['token'],
              userId: extras['userId'],
              discussionQue: extras['discussionQue'],
              channelName: extras['channelName'],
              userHandle: extras['userName'],
              profilePictureUrl: extras['profilePictureUrl'],
            ),
          );
        },
      ),
      GoRoute(
        path: '/subscription',
        name: 'subscription',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: SubscriptionPage(
              key: state.pageKey,
            ),
          );
        },
      ),
      GoRoute(
        path: '/sign-up',
        name: 'sign-up',
        pageBuilder: (context, state) {
          final extras = state.extra as Map;
          return TransitionPageWidget.navigateTransitionPage(
            child: SignUpPage(
              isForgotPassword: extras['isForgotPassword'],
            ),
          );
        },
      ),
      GoRoute(
        path: '/otp',
        name: 'otp',
        pageBuilder: (context, state) {
          final Map<String, dynamic>? extra =
              state.extra as Map<String, dynamic>?;
          final email = extra?['email'] as String?;
          final token = extra?['token'] as String?;
          return TransitionPageWidget.navigateTransitionPage(
            child: OTPPage(
              email: email ?? '',
              token: token ?? '',
              isForgotPassword: extra?['isForgotPassword'],
            ),
          );
        },
      ),
      GoRoute(
        path: '/set-password',
        name: 'set-password',
        pageBuilder: (context, state) {
          final Map<String, dynamic>? extra =
              state.extra as Map<String, dynamic>?;
          final email = extra?['email'] as String?;
          final token = extra?['token'] as String?;
          final otp = extra?['otp'] as String?;
          return TransitionPageWidget.navigateTransitionPage(
            child: SetPasswordPage(
              email: email ?? '',
              token: token ?? '',
              otp: otp ?? '',
              isForgotPassword: extra?['isForgotPassword'],
            ),
          );
        },
      ),
      GoRoute(
        path: '/set-name-handle',
        name: 'set-name-handle',
        pageBuilder: (context, state) {
          final Map<String, dynamic>? extra =
              state.extra as Map<String, dynamic>?;
          final email = extra?['email'] as String?;
          final token = extra?['token'] as String?;
          final otp = extra?['otp'] as String?;
          return TransitionPageWidget.navigateTransitionPage(
            child: NameAndHandlePage(
              email: email ?? '',
              token: token ?? '',
              otp: otp ?? '',
            ),
          );
        },
      ),
      GoRoute(
        path: '/profilesetup',
        name: 'profilesetup',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: ProfileSetupFinalStepPage(
              key: state.pageKey,
            ),
          );
        },
      ),
      GoRoute(
        path: "/chat-screen",
        name: "chat-screen",
        pageBuilder: (context, state) {
          final extras = state.uri.queryParameters;
          log('extra: ${extras['bookClubId']}');
          return TransitionPageWidget.navigateTransitionPage(
            child: FireBaseChat(
              // loggedInUserId: int.parse(extras['userId'] ?? ''),
              // bookClubName: extras['bookClubName'],
              bookClubId: int.tryParse(extras['bookClubId'] ?? '') ?? 0,
              key: state.pageKey,
            ),
          );
        },
      ),
      GoRoute(
        path: '/trial-delete-account',
        name: 'trial-delete-account',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: DeleteAccountPage(
              key: state.pageKey,
            ),
          );
        },
      ),
      GoRoute(
        path: '/privacy-policy',
        name: 'privacy-policy',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: PrivacyPolicyPage(
              key: state.pageKey,
            ),
          );
        },
      ),
      GoRoute(
        path: '/terms-of-service',
        name: 'terms-of-service',
        pageBuilder: (context, state) {
          return TransitionPageWidget.navigateTransitionPage(
            child: TermsOfServicePage(
              key: state.pageKey,
            ),
          );
        },
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) {
          return BottomNavigationWidget(
            navigationShell: navigationShell,
          );
        },
        branches: <StatefulShellBranch>[
          // HOME SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorHome,
            routes: [
              GoRoute(
                path: '/Home',
                name: 'Home',
                pageBuilder: (context, state) {
                  return CustomTransitionPage(
                    child: HomeScreen(
                      key: state.pageKey,
                    ),
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return SlideTransition(
                        position: animation.drive(
                          Tween(begin: const Offset(0, 1), end: Offset.zero)
                              .chain(
                            CurveTween(
                              curve: Curves.easeIn,
                            ),
                          ),
                        ),
                        child: child,
                      );
                    },
                  );
                },
                routes: [
                  GoRoute(
                    path: 'club-details',
                    name: 'club-details',
                    pageBuilder: (context, state) {
                      // final param = state.uri.queryParameters;
                      final extra = state.extra as Map;
                      final bookClubId = extra['bookClubId'];
                      final bookClubName = extra['bookClubName'];
                      final impromptuCount = extra['impromptuCount'];
                      // final isImpromptuClub = extra['isImpromptuClub'];
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ClubDetails(
                          key: state.pageKey,
                          bookClubId: bookClubId,
                          bookClubName: bookClubName,
                          impromptuCount: impromptuCount,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'club-charter',
                    name: 'club-charter',
                    pageBuilder: (context, state) {
                      final extra = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ClubCharter(
                          key: state.pageKey,
                          bookClubName: extra['bookClubName'],
                          clubCharter: extra['clubCharter'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'club-member-profile',
                    name: 'club-member-profile', //'HomeScreen4',
                    pageBuilder: (context, state) {
                      final extra = state.extra as Map;
                      // final userId = extra['userId'];
                      // final userName = extra['userName'];
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ClubMemberProfile(
                          key: state.pageKey,
                          userId: extra['userId'],
                          userName: extra['userName'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'book-review',
                    name: 'book-review',
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: BookReview(
                          key: state.pageKey,
                          userName: extras['userName'],
                          userHandle: extras['userHandle'],
                          userClubInvitation: extras['userClubInvitation'],
                          bookName: extras['bookName'],
                          bookAuthor: extras['bookAuthor'],
                          ratings: extras['ratings'],
                          review: extras['review'],
                          userProfilePicture: extras['userProfile'],
                          userOwnProfile: extras['userOwnProfile'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'home6',
                    name: 'HomeScreen6',
                    pageBuilder: (context, state) {
                      final memberName = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: HomeScreen6(
                          key: state.pageKey,
                          clubName: memberName,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'club-member-bookcase',
                    name: 'club-member-bookcase',
                    pageBuilder: (context, state) {
                      final Map<String, dynamic>? extra =
                          state.extra as Map<String, dynamic>?;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ClubMemberBookCase(
                          key: state.pageKey,
                          userName: extra?['userName'] as String?,
                          userHandle: extra?['userHandle'] as String?,
                          userClubInvitation:
                              extra?['userClubInvitation'] as bool?,
                          userId: extra?['userId'],
                          userProfilePicture: extra?['userProfilePicture'],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          // PROFILE SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorProfile,
            routes: [
              GoRoute(
                path: '/Profile',
                name: 'Profile',
                pageBuilder: (context, state) {
                  return TransitionPageWidget.navigateTransitionPage(
                    child: ProfileHomeScreen(
                      key: state.pageKey,
                    ),
                  );
                },
                routes: [
                  GoRoute(
                    path: 'EditProfileScreen',
                    name: 'EditProfileScreen',
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;

                      final UserModel userModel =
                          extras['userModel'] as UserModel;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: EditProfileScreen(
                          key: state.pageKey,
                          buttonName: extras['buttonName'],
                          userModel: userModel,
                          updateProfile: extras['isCompleteProfile'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'EditCurrentReadBook',
                    name: 'EditCurrentReadBook',
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: EditCurrentReadingBookScreen(
                          key: state.pageKey,
                          topShelfList: extras['topShelfList'],
                          buttonName: extras['buttonName'],
                          completedBookList: extras['completedBookList'],
                          userId: extras['userId'],
                          // currentbookCaseList: buttonName['currentRead'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'EditBookCase',
                    name: 'EditBookCase',
                    pageBuilder: (context, state) {
                      final caseData = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: EditBookCaseScreen(
                          key: state.pageKey,
                          userName: caseData['userName'],
                          userHandler: caseData['handler'],
                          lengthofTopShelf: caseData['topShelfLength'],
                          userClubInvitation: caseData['userClubInvitation'],
                          userProfilePicture: caseData['userProfilePicture'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'To-Be-Read',
                    name: 'To-Be-Read',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ToBeReadScreen(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          // MESSAGE SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorMessage,
            routes: [
              GoRoute(
                path: '/Message',
                name: 'Message',
                builder: (context, state) => MessagesScreen(key: state.pageKey),
                routes: [
                  GoRoute(
                    path: "message-user-club-details",
                    name: "message-user-club-details",
                    pageBuilder: (context, state) {
                      final queryParameters = state.uri.queryParameters;
                      final bookClubId = queryParameters['bookClubId'];
                      final userId = queryParameters['userId'];
                      // final extras = state.extra as Map<String, dynamic>;
                      return NoTransitionPage(
                        child: UserClubDetailsScreen(
                          key: state.pageKey,
                          bookClubId: int.parse(bookClubId ?? '0'),
                          fromChat: true,
                          userId: userId ?? '0',
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'message-club-member-profile',
                    name: 'message-club-member-profile',
                    pageBuilder: (context, state) {
                      final extra = state.extra as Map;
                      // final userId = extra['userId'];
                      // final userName = extra['userName'];
                      return NoTransitionPage(
                        child: ClubMemberProfile(
                          key: state.pageKey,
                          userId: extra['userId'],
                          // userName: extra['userName'],
                          fromChat: true,
                          bookClubId: extra['bookClubId'],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // CLUBS SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorClubs,
            routes: [
              GoRoute(
                path: '/Clubs',
                name: 'Clubs',
                builder: (context, state) =>
                    ClubsHomeScreen(key: state.pageKey),
                routes: [
                  GoRoute(
                    path: "club-invitations",
                    name: "club-invitations",
                    pageBuilder: (context, state) {
                      final query = state.uri.queryParameters;
                      final userId = query['userId'];
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ClubInvitations(
                          key: state.pageKey,
                          userId: int.parse(userId ?? '0'),
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "club-request",
                    name: "club-request",
                    pageBuilder: (context, state) {
                      // final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: OutGoingClubRequest(
                          key: state.pageKey,
                          // userId: extras['UserId'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "new-club-opening",
                    name: "new-club-opening",
                    pageBuilder: (context, state) {
                      // final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: NewClubOpeningScreen(
                          key: state.pageKey,
                          // userId: extras['UserId'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "NewClubScreen",
                    name: "NewClubScreen",
                    pageBuilder: (context, state) {
                      final invitation = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: NewBookClubScreen(
                          key: state.pageKey,
                          clubType: invitation,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "user-club-details",
                    name: "user-club-details",
                    pageBuilder: (context, state) {
                      final queryParameters = state.uri.queryParameters;
                      final bookClubId = queryParameters['bookClubId'];
                      final userId = queryParameters['userId'];
                      log("In Route BookClubId : $bookClubId");
                      // final extras = state.extra as Map<String, dynamic>;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: UserClubDetailsScreen(
                          key: state.pageKey,
                          bookClubId: int.parse(bookClubId ?? '0'),
                          userId: userId ?? '',
                        ),
                      );
                    },
                    routes: [
                      GoRoute(
                        path: "clubsScreen4",
                        name: "clubsScreen4",
                        pageBuilder: (context, state) {
                          final queryParameters = state.uri.queryParameters;
                          final bookClubId = queryParameters['bookClubId'];
                          // final extras = state.extra as Map;

                          return TransitionPageWidget.navigateTransitionPage(
                            child: ClubsScreen4(
                              key: state.pageKey,
                              bookClubId: int.parse(bookClubId ?? '0'),
                              // buttonName: extras['name'],
                              // clubFlag: extras['clubFlag'],
                            ),
                          );
                        },
                        routes: [
                          GoRoute(
                            path: "ManageIncomeRequest",
                            name: "ManageIncomeRequest",
                            pageBuilder: (context, state) {
                              final queryParameters = state.uri.queryParameters;
                              final bookClubId = queryParameters['bookClubId'];
                              final userId = queryParameters['userId'];
                              // final buttonName = state.extra.toString();
                              return TransitionPageWidget
                                  .navigateTransitionPage(
                                child: ManageIncomeRequestScreen(
                                  key: state.pageKey,
                                  bookClubId: int.parse(bookClubId ?? '0'),
                                  userId: userId ?? '',
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                  GoRoute(
                    path: "discussionScreen",
                    name: "discussionScreen",
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;

                      return TransitionPageWidget.navigateTransitionPage(
                        child: DiscussionQueScreen(
                          key: state.pageKey,
                          bookClubName: extras['clubName'],
                          discussionQuestions: extras['discussionQue'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "ManageOutGInvitations",
                    name: "ManageOutGInvitations",
                    pageBuilder: (context, state) {
                      final queryParameters = state.uri.queryParameters;
                      final bookClubId = queryParameters['bookClubId'];
                      final buttonName = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageOutGoningScreen(
                          key: state.pageKey,
                          buttonName: buttonName,
                          bookClubId: int.parse(bookClubId ?? '0'),
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "fellow-reader",
                    name: "fellow-reader",
                    pageBuilder: (context, state) {
                      // final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: FellowReaderScreen(
                          key: state.pageKey,
                          // userId: extras['UserId'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "ManageMember",
                    name: "ManageMember",
                    pageBuilder: (context, state) {
                      final buttonName = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageMemberScreen(
                          key: state.pageKey,
                          buttonName: buttonName,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "ManageMeeting",
                    name: "ManageMeeting",
                    pageBuilder: (context, state) {
                      final buttonName = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageMeetingScreen(
                          key: state.pageKey,
                          buttonName: buttonName,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "AddMeeting",
                    name: "AddMeeting",
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;
                      return TransitionPageWidget.navigateTransitionPage(
                        child: AddEditMeetingScreen(
                          key: state.pageKey,
                          addEditName: extras['addMeeting'],
                          upcomingMeetings: extras['editData'],
                          editMeetingFlag: extras['boolean'],
                          editMeetngId: extras['editMeetingId'],
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: "CharterRequest",
                    name: "CharterRequest",
                    pageBuilder: (context, state) {
                      final buttonName = state.extra.toString();
                      return TransitionPageWidget.navigateTransitionPage(
                        child: CharterMemberScreen(
                          key: state.pageKey,
                          buttonName: buttonName,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          // SEARCH SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorSearch,
            routes: [
              GoRoute(
                path: '/Search',
                name: 'Search',
                builder: (context, state) => SearchScreen(
                  key: state.pageKey,
                ),
                routes: [
                  GoRoute(
                    path: 'SearchResult',
                    name: 'SearchResult',
                    pageBuilder: (context, state) {
                      final extras = state.extra as Map;

                      return TransitionPageWidget.navigateTransitionPage(
                        child: ShowClubScreen(
                          key: state.pageKey,
                          bookIds: extras['bookIds'],
                          filterName: extras['filterName'],
                          bookAuthor: extras['bookAuthor'],
                          bookName: extras['bookName'],
                          // list: extras['list'],
                          // bookName: extras['bookName'],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          // SETTINGS SCREEN
          StatefulShellBranch(
            navigatorKey: _rootNavigatorSettings,
            routes: [
              GoRoute(
                path: '/Settings',
                name: 'Settings',
                builder: (context, state) => SettingScreen(key: state.pageKey),
                routes: [
                  GoRoute(
                    path: 'change-password',
                    name: 'change-password',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ChangePasswordPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'manage-email',
                    name: 'manage-email',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageEmailPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'notification-settings',
                    name: 'notification-settings',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: NotificationSettingsPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'block-list',
                    name: 'block-list',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: BlockListPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'manage-subscription',
                    name: 'manage-subscription',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: ManageSubscritionPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                  GoRoute(
                    path: 'delete-account',
                    name: 'delete-account',
                    pageBuilder: (context, state) {
                      return TransitionPageWidget.navigateTransitionPage(
                        child: DeleteAccountPage(
                          key: state.pageKey,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            navigatorKey: _rootNavigatorNoNetwork,
            routes: [
              GoRoute(
                path: '/no-network',
                name: 'no-network',
                pageBuilder: (context, state) {
                  final params = state.uri.queryParameters;
                  final targetIndex =
                      int.tryParse(params['targetIndex'] ?? '0') ?? 0;
                  return CustomTransitionPage(
                    transitionsBuilder:
                        (context, animation, secondaryAnimation, child) {
                      var begin = Offset(0.0, 1.0);
                      var end = Offset.zero;
                      var tween = Tween(begin: begin, end: end);
                      var offsetAnimation = animation.drive(tween);
                      return SlideTransition(
                        position: offsetAnimation,
                        child: child,
                      );
                    },
                    child: ConnectivityLossScreen(
                      onTryAgain: () async {
                        final status =
                            await InternetConnection().hasInternetAccess;
                        if (status) {
                          if (context.mounted) {
                            context.go('/Home');
                            // Navigate to the intended route based on targetIndex
                            switch (targetIndex) {
                              case 1:
                                context.go('/Profile');
                                break;
                              case 2:
                                context.go('/Message');
                                break;
                              case 3:
                                context.go('/Clubs');
                                break;
                              case 4:
                                context.go('/Search');
                                break;
                              case 5:
                                context.go('/Settings');
                                break;
                            }
                          }
                        }
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
      GoRoute(
        path: '/full-no-network',
        name: 'full-no-network',
        builder: (context, state) => ConnectivityLossScreen(
          showAppBar: false,
          onTryAgain: () async {
            bool isActiveSubscription = false;
            bool isLoggedIn = false;
            final connected = await InternetConnection().hasInternetAccess;
            if (context.mounted) {
              isActiveSubscription = await Provider.of<SubscriptionController>(
                      context,
                      listen: false)
                  .isActiveSubscription();
            }
            if (context.mounted) {
              isLoggedIn = await Provider.of<UserCredentialController>(context,
                      listen: false)
                  .isUserLoggedIn();
            }
            if (connected && context.mounted) {
              if (!isLoggedIn) {
                context.go('/login');
              } else if (isActiveSubscription) {
                context.go('/Home');
              } else {
                context.go('/subscription');
              }
            }
          },
        ),
      ),
      GoRoute(
        path: '/server-down',
        name: 'server-down',
        builder: (context, state) => ServerUnavailableScreen(
          onTryAgain: () async {
            bool isActiveSubscription = false;
            bool isLoggedIn = false;
            final connected = await InternetConnection().hasInternetAccess;
            if (context.mounted) {
              isActiveSubscription = await Provider.of<SubscriptionController>(
                      context,
                      listen: false)
                  .isActiveSubscription();
            }
            if (context.mounted) {
              isLoggedIn = await Provider.of<UserCredentialController>(context,
                      listen: false)
                  .isUserLoggedIn();
            }
            if (connected && context.mounted) {
              if (!isLoggedIn) {
                context.go('/login');
              } else if (isActiveSubscription) {
                context.go('/Home');
              } else {
                context.go('/subscription');
              }
            }
          },
        ),
      ),
    ],
  );
}
