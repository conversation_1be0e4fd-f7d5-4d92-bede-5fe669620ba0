import 'dart:async';
import 'dart:developer';

import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/models/book_case_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/marquee_text.dart';
import 'package:eljunto/reusableWidgets/question_feedback_dialog.dart';
import 'package:eljunto/reusable_api_function/books_api_functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:roundcheckbox/roundcheckbox.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/constants.dart';
import '../../controller/profile_controller.dart';
import '../../models/profile_model/edit_bookcase/listof_book_model.dart';
import '../../reusableWidgets/no_data_widget.dart';
import '../../reusableWidgets/paginated_book_typeahead.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';

class EditCurrentReadingBookScreen extends StatefulWidget {
  final String? buttonName;
  final int? userId;
  final List<BookCaseModel>? topShelfList;
  final List<BookCaseModel>? completedBookList;

  const EditCurrentReadingBookScreen({
    super.key,
    this.buttonName,
    this.userId,
    this.topShelfList,
    this.completedBookList,
  });

  @override
  State<EditCurrentReadingBookScreen> createState() =>
      _EditCurrentReadingBookScreenState();
}

class _EditCurrentReadingBookScreenState
    extends State<EditCurrentReadingBookScreen> {
  List<Books>? bookList = [];
  List<BookCaseModel> bookCaseList = [];
  List<BookCaseModel>? currentbookCaseList = [];
  List<BookCaseModel> completedBooks = [];
  bool currentReadLoading = false;
  int offset = 0;
  int currentReadLimit = 10;
  int currentReadcount = 0;

  int? loggedinUserId;
  bool isUnknown = false;
  double ratingStar = 1;
  int? bookId;
  String? bookName;
  String? bookAuthor;
  bool isBookIdNotEmpty = false;

  DateTime? selectedDate;
  bool istopShelfPopUpShow = false;
  bool ratingValidation = false;
  bool monthyearValidation = false;
  String? readingCompleteDate;
  SuggestionsController<Books>? suggestionsController;

  TextEditingController bookController = TextEditingController();
  TextEditingController reviewController = TextEditingController();
  TextEditingController completeDateController = TextEditingController();
  final selectTopShelfController = ValueNotifier<bool>(false);
  final unknownController = ValueNotifier<bool>(false);
  List<String> filteredBookList = [];
  List<BookCaseModel>? topShelfList;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool alreadyExists = false;
  String responseMessage = '';
  String markAsComplete = "Mark as Complete";
  String updateCompletionDate = "Update Completion Date";
  Future<void>? data;
  final ScrollController _scrollControllerCurrentRead = ScrollController();
  int limit = 10;
  int offSet = 0;
  BookCaseController? bookCaseController;
  int? bookCaseId;

  @override
  void initState() {
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
    _scrollControllerCurrentRead.addListener(_onScrollCurrentRead);
    _initializeUserId();
    getBookCase();
    super.initState();
  }

  @override
  void dispose() {
    suggestionsController?.dispose();
    _scrollControllerCurrentRead.removeListener(_onScrollCurrentRead);
    completeDateController.dispose();
    reviewController.dispose();
    bookController.dispose();
    selectTopShelfController.dispose();
    unknownController.dispose();
    super.dispose();
  }

  Future<void> _initializeUserId() async {
    loggedinUserId = await CommonHelper.getLoggedInUserId();
  }

  ProfileController? profileController;
  int bookListCount = 0;
  int bookListLimit = 10;
  bool isBookLoading = false;
  String searchValue = '';

  Future<List<Books>> clearList() async {
    bookList = [];
    return bookList ?? [];
  }

  Future<void> getBookCase() async {
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .allBooksRead(
              widget.userId ?? 0, 10000, offset, false, false, context)
          .then((responseMap) async {
        log("Complete Book Length");

        if (responseMap["statusCode"] == 200) {
          List<BookCaseModel> bookList = [];
          log("Complete Book  : ${responseMap['data']}");
          List<dynamic> list = responseMap['data'];

          bookList = (list)
              .map((item) =>
                  BookCaseModel.fromJson(item as Map<String, dynamic>))
              .toList();
          log("Complete Book Length1 : ${bookList.length}");

          var result =
              CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
          if (result.isNotEmpty) {
            completedBooks = result[2];
          }
          log("Complete Book Length2: ${completedBooks.length}");
        } else {}
      });
    } catch (e) {
      log(e.toString());
    }
  }

  void _onScrollCurrentRead() {
    if (_scrollControllerCurrentRead.position.pixels ==
            _scrollControllerCurrentRead.position.maxScrollExtent &&
        !currentReadLoading &&
        (currentbookCaseList?.length ?? 0) < currentReadcount) {
      CommonHelper.networkClose(getCurrentReadBookCase(true), context);

      // getCurrentReadBookCase(true);
    }
  }

  Future<void> getCurrentReadBookCase(bool isMore) async {
    if ((currentbookCaseList?.length ?? 0) <= currentReadcount || !isMore) {
      currentReadLoading = true;

      if (isMore) {
        currentReadLimit += 10; // Increment the limit by 10 for the next load
      }
    }
    log("Current Read Limit : $currentReadLimit");
    try {
      await Provider.of<BookCaseController>(context, listen: false)
          .getCurrentReadBookCase(
              widget.userId ?? 0, currentReadLimit, offset, context)
          .then((responseMap) async {
        log("Response Map : ${responseMap['statusCode']}");
        if (responseMap["statusCode"] == 200) {
          // List<BookCaseModel> bookList = [];
          if (responseMap["count"] != null) {
            currentReadcount = responseMap['count'];
          } else {
            currentReadcount = 0;
          }
          log("Current Read Count : $currentReadcount");

          if (responseMap["data"] != null) {
            bookCaseList = (responseMap["data"] as List)
                .map((item) => BookCaseModel.fromJson(item))
                .toList();
            currentbookCaseList = bookCaseList;
            if (mounted) {
              Provider.of<BookCaseController>(context, listen: false)
                  .notifyListeners();
            }
          } else {
            currentbookCaseList?.clear();
            currentbookCaseList = [];
          }
          log("BookCase List : ${bookCaseList.length}");
        } else if (responseMap['statusCode'] == 404) {
          log("Current Read : ${currentbookCaseList?.length}");
          currentbookCaseList?.clear();
          currentbookCaseList = [];
          currentReadcount = 0;
          topShelfList = [];
        }
      }).whenComplete(() {
        currentReadLoading = false;
      });
      log("Current Read : ${currentbookCaseList?.length}");
    } catch (e) {
      log(e.toString());
    }
  }

  bool isAlreadyExist = false;

  Future<bool> confirmAddBook() async {
    BookCaseModel addBook = BookCaseModel(
      // userId: loggedinUserId,
      bookId: bookId,
      bookAuthor: bookAuthor,
      bookName: bookName,
      is_currently_reading: true,
    );
    await Provider.of<BookCaseController>(context, listen: false)
        .addBookInBookCase(addBook, context)
        .then((value) {
      final tempMessage = bookCaseController?.addBookErrorMessage ?? '';
      if (value == 'exist') {
        if (tempMessage
                .toLowerCase()
                .contains('currently in your all-books-read') ||
            tempMessage
                .toLowerCase()
                .contains('currently in your to-be-read')) {
          isAlreadyExist = true;
        } else {
          isAlreadyExist = false;
        }
        responseMessage = bookCaseController?.addBookErrorMessage ?? '';
        bookCaseId = bookCaseController?.bookCaseId;
        alreadyExists = true;
      } else {
        alreadyExists = false;
      }
    });
    // }
    // }
    return alreadyExists;
  }

  Future<void> confirmUpdateBook(int bookId) async {
    BookCaseModel updatedBook = BookCaseModel();
    if (responseMessage.contains('all-books-read')) {
      if (completedBooks.isNotEmpty) {}
      BookCaseModel? bookToReRead =
          completedBooks.firstWhere((book) => book.bookId == bookId);
      updatedBook = BookCaseModel(
        bookCaseId: bookToReRead.bookCaseId,
        userId: bookToReRead.userId,
        bookId: bookToReRead.bookId,
        bookName: bookToReRead.bookName,
        bookAuthor: bookToReRead.bookAuthor,
        is_currently_reading: true,
        readingCompleteDate: bookToReRead.readingCompleteDate,
        ratings: bookToReRead.ratings,
        topShelf: bookToReRead.topShelf,
        review: bookToReRead.review,
        reRead: (bookToReRead.reRead ?? 0) + 1,
      );
      // await Provider.of<BookCaseController>(context, listen: false)
      //     .updateBookCase(updatedBook, context)
      //     .then((value) async {
      //   if (value) {
      //   } else {}
      // });
    } else {
      updatedBook = BookCaseModel(
        bookCaseId: bookCaseId,
        userId: loggedinUserId,
        bookId: bookId,
        // bookName: bookToReRead.bookName,
        // bookAuthor: bookToReRead.bookAuthor,
        is_currently_reading: true,
        toBeRead: false,
        // readingCompleteDate: bookToReRead.readingCompleteDate,
        // ratings: bookToReRead.ratings,
        // topShelf: bookToReRead.topShelf,
        // review: bookToReRead.review,
        reRead: 0,
      );
    }
    await Provider.of<BookCaseController>(context, listen: false)
        .updateBookCase(updatedBook, context)
        .then((value) async {
      if (value) {
      } else {}
    });
  }

  Future<void> confirmMarkAsComplete(
      int? bookCaseId, int? bookId, String editText) async {
    //final date = DateFormat("dd-MM-yyyy").format(selectedDate!);
    String? date;
    if (selectedDate != null) {
      date = DateFormat("dd-MM-yyyy").format(selectedDate!);
    }

    BookCaseModel? bookToMarkComplete =
        currentbookCaseList?.firstWhere((book) => book.bookId == bookId);
    if (bookToMarkComplete == null) return;

// Create a base object with common properties
    BookCaseModel updatedBook = BookCaseModel(
      bookCaseId: bookToMarkComplete.bookCaseId,
      userId: bookToMarkComplete.userId,
      bookId: bookToMarkComplete.bookId,
      bookName: bookToMarkComplete.bookName,
      bookAuthor: bookToMarkComplete.bookAuthor,
      is_currently_reading: false,
      reading_complete_date_String: date,
      // ratings, top shelf, review, reRead Default to existing value
      ratings: bookToMarkComplete.ratings,
      topShelf: bookToMarkComplete.topShelf,
      review: bookToMarkComplete.review,
      reRead: bookToMarkComplete.reRead,
    );
    // Modify additional properties if editText is Mark as Complete
    if (editText == "Mark as Complete") {
      //bookToMarkComplete.ratings = ratingStar;
      updatedBook = BookCaseModel(
        bookCaseId: bookToMarkComplete.bookCaseId,
        userId: bookToMarkComplete.userId,
        bookId: bookToMarkComplete.bookId,
        bookName: bookToMarkComplete.bookName,
        bookAuthor: bookToMarkComplete.bookAuthor,
        is_currently_reading: false,
        reading_complete_date_String: date,
        // New value for ratings, top shelf, review
        ratings: ratingStar,
        topShelf: selectTopShelfController.value,
        review: reviewController.text,
        reRead: bookToMarkComplete.reRead,
      );
    }

    await Provider.of<BookCaseController>(context, listen: false)
        .updateBookCase(updatedBook, context)
        .then((_) {
      // getCurrentReadBookCase(false);
      setState(() {});
    });
  }

  bool isBookExistsInBookCase(int? bookCaseId) {
    bool bookExists = false;
    if (completedBooks.isNotEmpty) {
      bookExists = completedBooks.any((book) => book.bookCaseId == bookCaseId);
    }
    return bookExists;
  }

  Future<void> confirmDelete(int? bookId) async {
    bool bookExistsInBookCase = false;
    if (completedBooks.isNotEmpty) {
      bookExistsInBookCase =
          completedBooks.any((book) => book.bookId == bookId);
    }
    if (bookExistsInBookCase) {
      BookCaseModel? bookToRemoveFromCurrReading =
          bookCaseList.firstWhere((book) => book.bookId == bookId);
      log("Bookcase ratings : ${bookToRemoveFromCurrReading.bookId}");
      BookCaseModel updatedBook = BookCaseModel(
        bookCaseId: bookToRemoveFromCurrReading.bookCaseId,
        userId: bookToRemoveFromCurrReading.userId,
        bookId: bookToRemoveFromCurrReading.bookId,
        bookName: bookToRemoveFromCurrReading.bookName,
        bookAuthor: bookToRemoveFromCurrReading.bookAuthor,
        is_currently_reading: false,
        readingCompleteDate: bookToRemoveFromCurrReading.readingCompleteDate,
        ratings: bookToRemoveFromCurrReading.ratings,
        topShelf: bookToRemoveFromCurrReading.topShelf,
        review: bookToRemoveFromCurrReading.review,
        reRead: bookToRemoveFromCurrReading.reRead,
      );
      await Provider.of<BookCaseController>(context, listen: false)
          .updateBookCase(updatedBook, context)
          .then((value) async {
        setState(() {});

        if (value) {
          log('Book removed from currntly reading');
        } else {}
      });
    } else {
      await Provider.of<BookCaseController>(context, listen: false)
          .deleteBook(bookId, context)
          .then((value) {
        setState(() {});
      });
    }
    // getCurrentReadBookCase(false);
  }

  String getEditLinkText(BookCaseModel? book) {
    String editLinkText = '';
    if (book?.readingCompleteDate != null) {
      editLinkText = "";
    } else if (book?.reRead != null && (book?.reRead ?? 0) >= 1) {
      editLinkText = updateCompletionDate;
    } else {
      editLinkText = markAsComplete;
    }
    return editLinkText;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(80),
        child: Container(
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                width: 1.5,
                color: AppConstants.primaryColor,
              ),
            ),
          ),
          child: PreviousScreenAppBar(
            bookName: widget.buttonName,
            isSetProfile: true,
          ),
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              AppConstants.bgImagePath,
            ),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fitWidth,
          ),
        ),
        child: FutureBuilder(
          future: getCurrentReadBookCase(false),
          builder: (context, snapShot) {
            return Skeletonizer(
              effect: const SoldColorEffect(
                color: AppConstants.skeletonforgroundColor,
                lowerBound: 0.1,
                upperBound: 0.5,
              ),
              containersColor: AppConstants.skeletonBackgroundColor,
              enabled: snapShot.connectionState == ConnectionState.waiting,
              child: Column(
                children: [
                  const SizedBox(
                    height: 25,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NetworkAwareTap(
                          onTap: () {
                            showAddBookPopUp(() {
                              setState(() {});
                            });
                          },
                          child: Text(
                            'Add new book +',
                            style: lbItalic.copyWith(
                              fontSize: 16,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  currentbookCaseList?.isNotEmpty ?? false
                      ? Expanded(
                          child: Consumer<BookCaseController>(
                            builder: (context, bookCaseController, child) {
                              return ListView.builder(
                                controller: _scrollControllerCurrentRead,
                                padding: const EdgeInsets.only(bottom: 25),
                                itemCount: currentReadLoading
                                    ? (currentbookCaseList?.length ?? 0) + 1
                                    : (currentbookCaseList?.length ?? 0),
                                itemBuilder: (context, index) {
                                  // Safely access the book
                                  // final book = currentbookCaseList?[index];
                                  // if (currentbookCaseList?[index] == null) {
                                  //   return const SizedBox.shrink();
                                  // }

                                  if (index == currentbookCaseList?.length &&
                                      currentReadLoading) {
                                    return const Padding(
                                      padding: EdgeInsets.only(left: 10.0),
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                    );
                                  }

                                  return Skeleton.replace(
                                    replacement:
                                        cardWidgetSkeleton(index, true),
                                    child: cardWidgetSkeleton(index, false),
                                  );
                                },
                              );
                            },
                          ),
                        )
                      : Skeleton.replace(
                          replacement: emptyCardSkeleton(),
                          child: const Padding(
                            padding: EdgeInsets.only(top: 25.0),
                            child: NoDataWidget(
                              message: "No books in currently reading",
                            ),
                          ),
                        ),
                  //],
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget cardWidgetSkeleton(int index, bool isBorder) {
    final editLinkText =
        getEditLinkText(currentbookCaseList?[index] ?? BookCaseModel());
    log("Edit Text : $editLinkText");
    return Container(
      height: 117,
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.only(top: 25, left: 20, right: 20),
      decoration: BoxDecoration(
        color: isBorder
            ? AppConstants.skeletonBackgroundColor
            : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: isBorder ? Colors.transparent : AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MarqueeList(
              children: [
                Text(
                  currentbookCaseList?[index].bookName ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: lbBold.copyWith(
                    fontSize: 18,
                  ),
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              currentbookCaseList?[index].bookAuthor ?? '',
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(
                fontSize: 14,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              children: [
                NetworkAwareTap(
                  onTap: () async {
                    final bookName = currentbookCaseList?[index].bookName;
                    final author = currentbookCaseList?[index].bookAuthor;
                    final bookId = currentbookCaseList?[index].bookId;

                    await showMarkAsCompletePopup(
                      index,
                      bookId,
                      bookName,
                      author,
                      editLinkText,
                      () {
                        setState(() {});
                      },
                    );
                  },
                  child: Text(
                    editLinkText,
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decoration: TextDecoration.underline,
                      fontSize: 14,
                    ),
                  ),
                ),
                const Spacer(),
                NetworkAwareTap(
                  onTap: () {
                    showDeletePopUp(index);
                  },
                  child: Text(
                    'Delete',
                    overflow: TextOverflow.ellipsis,
                    style: lbItalic.copyWith(
                      decorationColor: AppConstants.redColor,
                      decoration: TextDecoration.underline,
                      fontSize: 14,
                      color: AppConstants.redColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget emptyCardSkeleton() {
    return Container(
      padding: const EdgeInsets.only(left: 20),
      margin: const EdgeInsets.only(left: 20, right: 20, top: 25),
      height: 50,
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: AppConstants.skeletonBackgroundColor,
        borderRadius: BorderRadius.circular(10),
      ),
      child: const Align(
        alignment: Alignment.centerLeft,
        child: Text(
          "No books in currently reading",
          textAlign: TextAlign.start,
        ),
      ),
    );
  }

  Future<void> showDeletePopUp(int index) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(25),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            Column(
              children: [
                NetworkAwareTap(
                  onTap: () {
                    context.pop();
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    padding: const EdgeInsets.only(top: 10),
                    child: Image.asset(
                      AppConstants.closePopupImagePath,
                      height: 30,
                      width: 30,
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Delete Book:",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "Are you sure you want to delete this book?",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 30.0, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      NetworkAwareTap(
                        onTap: () async {
                          final bookId = currentbookCaseList?[index].bookId;
                          await confirmDelete(bookId);

                          if (context.mounted) {
                            context.pop();
                          }
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.textGreenColor,
                          ),
                          child: Center(
                            child: Text(
                              "Delete",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                      NetworkAwareTap(
                        onTap: () {
                          context.pop();
                          // notSentInvitationFunction();
                        },
                        child: Container(
                          height: 45,
                          width: MediaQuery.of(context).size.width / 3,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(49),
                            color: AppConstants.backgroundColor,
                            border: Border.all(
                              color: AppConstants.primaryColor,
                            ),
                          ),
                          child: Center(
                            child: Text(
                              "Cancel",
                              textAlign: TextAlign.center,
                              style: lbBold.copyWith(
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
              ],
            )
          ],
        );
      },
    );
  }

  Future<void> showMarkAsCompletePopup(int index, int? bookId, String? bookName,
      String? author, String editText, Function updateUI) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        reviewController.clear();
        completeDateController.clear();
        ratingValidation = false;
        monthyearValidation = false;
        ratingStar = 0.5;
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Center(
            child: SingleChildScrollView(
              child: StatefulBuilder(builder: (context, setState) {
                return AlertDialog(
                  actionsPadding: const EdgeInsets.only(right: 10),
                  insetPadding: const EdgeInsets.all(25),
                  contentPadding: EdgeInsets.zero,
                  backgroundColor: AppConstants.backgroundColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                    side: const BorderSide(
                      color: AppConstants.popUpBorderColor,
                      width: 1.5,
                    ),
                  ),
                  surfaceTintColor: Colors.white,
                  actions: [
                    Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(top: 10),
                              child: Image.asset(
                                AppConstants.closePopupImagePath,
                                height: 30,
                                width: 30,
                              ),
                            ),
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.only(left: 22.0, right: 12),
                            child: Column(
                              children: [
                                SizedBox(
                                  width: MediaQuery.of(context).size.width,
                                  child: Text(
                                    editText == markAsComplete
                                        ? "Mark as Complete"
                                        : "Update Completion Date",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                                if (editText == markAsComplete) ...[
                                  const SizedBox(
                                    height: 3,
                                  ),
                                  Text(
                                    "(Removes book from “Currently Reading” and Adds to “Books Read”)",
                                    textAlign: TextAlign.center,
                                    style: lbRegular.copyWith(
                                      fontSize: 12,
                                    ),
                                  )
                                ],
                                const SizedBox(
                                  height: 25,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "Book: $bookName, $author",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                editText == markAsComplete
                                    ? Text(
                                        "Last Read:",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Select completion date:",
                                      textAlign: TextAlign.center,
                                      style: lbRegular.copyWith(
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          // height: 30,
                                          width: MediaQuery.of(context)
                                                  .size
                                                  .width /
                                              2.8,
                                          child: TextFormField(
                                            controller: completeDateController,
                                            style: lbRegular.copyWith(
                                              fontSize: 12,
                                            ),
                                            decoration: InputDecoration(
                                              suffixIcon: const Icon(
                                                Icons.calendar_month_outlined,
                                                size: 20,
                                              ),
                                              contentPadding:
                                                  const EdgeInsets.all(10),
                                              fillColor: const Color.fromRGBO(
                                                  255, 255, 255, 1),
                                              filled: true,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(5),
                                                borderSide: const BorderSide(
                                                  color:
                                                      AppConstants.primaryColor,
                                                  width: 1.5,
                                                ),
                                              ),
                                            ),
                                            onChanged: (value) {
                                              setState(
                                                () {
                                                  monthyearValidation = false;
                                                },
                                              );
                                            },
                                            onTap: () async {
                                              selectedDate = await CommonHelper
                                                  .getMonthYear(context);
                                              setState(() {
                                                if (selectedDate != null) {
                                                  monthyearValidation = false;
                                                  unknownController.value =
                                                      false;
                                                  completeDateController.text =
                                                      DateFormat('MMM yyyy')
                                                          .format(
                                                              selectedDate!);
                                                  log("Date : ${completeDateController.text}");
                                                  readingCompleteDate =
                                                      completeDateController
                                                          .text;
                                                } else {
                                                  completeDateController
                                                      .clear();
                                                  readingCompleteDate = '';
                                                }
                                              });
                                            },
                                            readOnly: true,
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 25,
                                        ),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Text(
                                              "Unknown",
                                              textAlign: TextAlign.center,
                                              style: lbRegular.copyWith(
                                                fontSize: 12,
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 10,
                                            ),
                                            RoundCheckBox(
                                              isChecked:
                                                  unknownController.value,
                                              border: Border.all(
                                                  color: Colors.transparent),
                                              onTap: (p0) {
                                                setState(
                                                  () {
                                                    unknownController.value =
                                                        p0!;
                                                    if (unknownController
                                                        .value) {
                                                      monthyearValidation =
                                                          false;
                                                      completeDateController
                                                          .clear();
                                                    }

                                                    log("UnKnown value : ${unknownController.value}");
                                                    monthyearValidation = false;
                                                  },
                                                );
                                              },
                                              checkedWidget: const Icon(
                                                Icons
                                                    .check_circle_outline_rounded,
                                                color:
                                                    AppConstants.primaryColor,
                                              ),
                                              checkedColor:
                                                  AppConstants.backgroundColor,
                                              uncheckedColor:
                                                  AppConstants.backgroundColor,
                                              uncheckedWidget: const Icon(
                                                Icons.circle_outlined,
                                                color:
                                                    AppConstants.primaryColor,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    Positioned(
                                      top: 55,
                                      left: 0,
                                      right: 0,
                                      child: monthyearValidation
                                          ? Text(
                                              "*Select a date or check 'Unknown'",
                                              style: lbRegular.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                monthyearValidation
                                    ? const SizedBox(
                                        height: 25,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(
                                  height: 15,
                                ),
                                if (editText == markAsComplete) ...[
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Ratings:",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      RatingBar(
                                        glow: false,
                                        itemCount: 5,
                                        itemSize: 25,
                                        allowHalfRating: true,
                                        initialRating: ratingStar,
                                        minRating: 0.5,
                                        unratedColor: Colors.red,
                                        ratingWidget: RatingWidget(
                                          full: const Icon(
                                            Icons.star,
                                            color: AppConstants.textGreenColor,
                                          ),
                                          half: const Icon(
                                            Icons.star_half,
                                            color: AppConstants.textGreenColor,
                                          ),
                                          empty: const Icon(
                                            Icons.star_border_outlined,
                                            color: AppConstants.textGreenColor,
                                          ),
                                        ),
                                        onRatingUpdate: (double value) {
                                          setState(() {
                                            ratingValidation = false;
                                            ratingStar = value;
                                            log("Rating : $ratingStar");
                                          });
                                        },
                                      ),
                                      const SizedBox(
                                        width: 5,
                                      ),
                                      ratingValidation
                                          ? Text(
                                              '*',
                                              style: lbRegular.copyWith(
                                                color: AppConstants.redColor,
                                                fontSize: 20,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 15,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Up to 2000 Character Review (Optional): ",
                                        textAlign: TextAlign.center,
                                        style: lbRegular.copyWith(
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  TextFormField(
                                    controller: reviewController,
                                    maxLines: 4,
                                    maxLength: 2000,
                                    style: lbRegular.copyWith(
                                      fontSize: 12,
                                    ),
                                    decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.all(10),
                                      counterStyle: lbRegular.copyWith(
                                        fontSize: 14,
                                      ),
                                      fillColor: const Color.fromRGBO(
                                          255, 255, 255, 1),
                                      filled: true,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(5),
                                        borderSide: const BorderSide(
                                          color: AppConstants.primaryColor,
                                          width: 1.5,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                                const SizedBox(
                                  height: 25,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    NetworkAwareTap(
                                      onTap: () async {
                                        bool validation =
                                            _formKey.currentState!.validate();

                                        if (validation) {
                                          if (completeDateController
                                                  .text.isEmpty &&
                                              unknownController.value ==
                                                  false) {
                                            setState(
                                              () {
                                                monthyearValidation = true;
                                              },
                                            );
                                          } else if (editText ==
                                                  markAsComplete &&
                                              ratingStar == 0) {
                                            setState(
                                              () {
                                                ratingValidation = true;
                                              },
                                            );
                                          } else {
                                            final bookCaseId =
                                                currentbookCaseList?[index]
                                                    .bookCaseId;
                                            await confirmMarkAsComplete(
                                                bookCaseId, bookId, editText);
                                            if (context.mounted) {
                                              updateUI();
                                              context.pop();
                                            }
                                          }
                                        }
                                      },
                                      child: Container(
                                        height: 45,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                3,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                          color: AppConstants.textGreenColor,
                                        ),
                                        child: Center(
                                          child: Text(
                                            editText == markAsComplete
                                                ? "Add"
                                                : "Update",
                                            textAlign: TextAlign.center,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    NetworkAwareTap(
                                      onTap: () {
                                        reviewController.clear();
                                        completeDateController.clear();
                                        context.pop();
                                        // notSentInvitationFunction();
                                      },
                                      child: Container(
                                        height: 45,
                                        width:
                                            MediaQuery.of(context).size.width /
                                                3,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(49),
                                          color: AppConstants.backgroundColor,
                                          border: Border.all(
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            "Cancel",
                                            textAlign: TextAlign.center,
                                            style: lbBold.copyWith(
                                              fontSize: 18,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 30,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Future<void> showAddBookPopUp(Function updateUI) async {
    bookController.clear();
    bookCaseController?.updateTypeAheadFlag(false);
    isBookIdNotEmpty = false;
    bookListCount = 0;
    bookListLimit = 10;
    bookId = 0;
    bookName = null;
    bookAuthor = null;
    bookList = [];
    suggestionsController?.dispose();
    suggestionsController = null;
    suggestionsController = SuggestionsController();

    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            actionsPadding: const EdgeInsets.only(right: 10),
            insetPadding: const EdgeInsets.all(20),
            contentPadding: EdgeInsets.zero,
            backgroundColor: AppConstants.backgroundColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.popUpBorderColor,
                width: 1.5,
              ),
            ),
            surfaceTintColor: Colors.white,
            actions: [
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    NetworkAwareTap(
                      onTap: () {
                        bookController.clear(); // Clear when closing
                        bookCaseController?.updateTypeAheadFlag(false);
                        isBookIdNotEmpty = false;
                        bookId = 0;
                        bookName = null;
                        bookAuthor = null;
                        bookList = [];
                        suggestionsController?.dispose();
                        suggestionsController = null;
                        context.pop();
                      },
                      child: Container(
                        alignment: Alignment.centerRight,
                        padding: const EdgeInsets.only(top: 10),
                        child: Image.asset(
                          AppConstants.closePopupImagePath,
                          height: 30,
                          width: 30,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: Text(
                          "Add New Book",
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                    ),
                    Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 20.0, left: 30),
                          child: Consumer<ProfileController>(
                              builder: (context, profileController, child) {
                            if (suggestionsController != null) {
                              return PaginatedBookTypeahead(
                                suggestionsController: suggestionsController,
                                controller: bookController,
                                autofocus: true,
                                showDiacriticWarning: false,
                                fetchBooksCallback: (query, offset, limit) {
                                  // This function handles both initial load and pagination
                                  return BooksApiFunctions.fetchBooks(
                                    query,
                                    offset,
                                    limit,
                                    context,
                                    loggedinUserId,
                                  );
                                },
                                onSelected: (book) {
                                  if (book.bookName ==
                                      "Can't Find Your Book? Click Here") {
                                    questionFeedBox();
                                  } else {
                                    _handleBookSelection(book);
                                  }
                                },
                                onCantFindBook: () {
                                  questionFeedBox();
                                },
                              );
                            } else {
                              return Container();
                            }
                          }),
                        ),
                        Consumer<BookCaseController>(
                            builder: (context, bookCaseController, child) {
                          return Positioned(
                            top: 55,
                            left: 30,
                            right: 0,
                            child: bookCaseController.isTypeAheadEmpty
                                ? Text(
                                    "*Select book",
                                    style: lbRegular.copyWith(
                                      fontSize: 14,
                                      color: AppConstants.redColor,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          );
                        }),
                        Positioned(
                          top: 55,
                          left: 30,
                          right: 0,
                          child: isBookIdNotEmpty && bookId == 0
                              ? Text(
                                  "*Invalid book",
                                  style: lbRegular.copyWith(
                                    fontSize: 14,
                                    color: AppConstants.redColor,
                                  ),
                                )
                              : const SizedBox.shrink(),
                        ),
                      ],
                    ),
                    Consumer<BookCaseController>(
                        builder: (context, bookCaseController, child) {
                      return bookCaseController.isTypeAheadEmpty
                          ? const SizedBox(
                              height: 15,
                            )
                          : const SizedBox.shrink();
                    }),
                    isBookIdNotEmpty
                        ? const SizedBox(
                            height: 15,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(
                      height: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          NetworkAwareTap(
                            onTap: () async {
                              bool validation =
                                  _formKey.currentState!.validate();
                              if (bookController.text.isEmpty) {
                                setState(() {
                                  bookCaseController?.updateTypeAheadFlag(true);
                                });
                                return;
                              }

                              if (isBookIdNotEmpty && validation) {
                                await confirmAddBook().then(
                                  (value) async {
                                    updateUI();
                                    if (context.mounted) {
                                      context.pop();
                                    }

                                    if (value) {
                                      // context.pop();
                                      addNewBook(updateUI);
                                      // await showDialog(
                                      //   barrierColor: Colors.white60,
                                      //   context: context,
                                      //   barrierDismissible: false,
                                      //   builder: (BuildContext context) {
                                      //     return CustomDialog(
                                      //       title: "Add New Book",
                                      //       message: responseMessage,
                                      //       showDoneImage: false,
                                      //     );
                                      //   },
                                      // );
                                    }
                                  },
                                );
                              } else {
                                setState(() {
                                  isBookIdNotEmpty = true;
                                });
                              }
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.textGreenColor,
                              ),
                              child: Center(
                                child: Text(
                                  "Add",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          NetworkAwareTap(
                            onTap: () {
                              context.pop();
                            },
                            child: Container(
                              height: 45,
                              width: MediaQuery.of(context).size.width / 3,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(49),
                                color: AppConstants.backgroundColor,
                                border: Border.all(
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  "Cancel",
                                  textAlign: TextAlign.center,
                                  style: lbBold.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              )
            ],
          );
        });
      },
    );
  }

  Future<void> addNewBook(Function updatedUI) async {
    await showCupertinoModalPopup(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          actionsPadding: const EdgeInsets.only(right: 10),
          insetPadding: const EdgeInsets.all(20),
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppConstants.backgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.popUpBorderColor,
              width: 1.5,
            ),
          ),
          surfaceTintColor: Colors.white,
          actions: [
            NetworkAwareTap(
              onTap: () {
                context.pop();
              },
              child: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(top: 10),
                child: Image.asset(
                  AppConstants.closePopupImagePath,
                  height: 30,
                  width: 30,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 30.0, right: 20),
              child: Column(
                children: [
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      "Add New Book",
                      textAlign: TextAlign.center,
                      style: lbRegular.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          responseMessage,
                          textAlign: TextAlign.center,
                          style: lbRegular.copyWith(
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 25,
                  ),
                  isAlreadyExist && alreadyExists
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            NetworkAwareTap(
                              onTap: () async {
                                // await getBookCaseId(bookId).then((value) {
                                // bookCaseId = value;
                                await confirmUpdateBook(bookId ?? 0);
                                // });

                                if (context.mounted) {
                                  context.pop();
                                  updatedUI();
                                }
                              },
                              child: buttonUI('Move'),
                            ),
                            NetworkAwareTap(
                              onTap: () {
                                context.pop();
                                // notSentInvitationFunction();
                              },
                              child: buttonUI('Cancel'),
                            ),
                          ],
                        )
                      : NetworkAwareTap(
                          onTap: () {
                            context.pop();
                          },
                          child: buttonUI('OK'),
                        ),
                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Widget buttonUI(String buttonName) {
    return Container(
      height: 45,
      width: MediaQuery.of(context).size.width / 3,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(49),
        color: AppConstants.textGreenColor,
      ),
      child: Center(
        child: Text(
          buttonName,
          textAlign: TextAlign.center,
          style: lbBold.copyWith(
            fontSize: 18,
          ),
        ),
      ),
    );
  }

  void questionFeedBox() {
    showDialog(
      barrierColor: Colors.white60,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const QuestionFeedbackDialog(isBookQuery: true);
      },
    );
  }

  void _handleBookSelection(Books book) {
    setState(() {
      bookController.text = book.bookName.toString();
      bookId = book.bookId;
      bookName = book.bookName ?? '';
      bookAuthor = book.bookAuthor ?? '';
      isBookIdNotEmpty = true;
    });
  }
}
