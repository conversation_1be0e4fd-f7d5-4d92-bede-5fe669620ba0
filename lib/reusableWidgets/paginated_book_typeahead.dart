import 'dart:async';
import 'dart:developer';

import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/book_case_controller.dart';
import 'package:eljunto/models/profile_model/edit_bookcase/listof_book_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/services/diacritics_remover.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:provider/provider.dart';

class PaginatedBookTypeahead extends StatefulWidget {
  final SuggestionsController<Books>? suggestionsController;
  final TextEditingController controller;
  final Future<List<Books>> Function(String query, int offset, int limit)
      fetchBooksCallback;
  final void Function(Books) onSelected;
  final bool autofocus;
  final InputDecoration? decoration;
  final TextStyle? textStyle;
  final int minCharsForSuggestions;
  final bool showDiacriticWarning;
  final Function()? onCantFindBook;
  final bool isAddNewMeeting;

  const PaginatedBookTypeahead({
    super.key,
    required this.suggestionsController,
    required this.controller,
    required this.fetchBooksCallback,
    required this.onSelected,
    this.autofocus = false,
    this.decoration,
    this.textStyle,
    this.minCharsForSuggestions = 3,
    this.showDiacriticWarning = false,
    this.onCantFindBook,
    this.isAddNewMeeting = false,
  });

  @override
  State<PaginatedBookTypeahead> createState() => _PaginatedBookTypeaheadState();
}

class _PaginatedBookTypeaheadState extends State<PaginatedBookTypeahead> {
  final _textCleaner = TextCleaner();
  final ScrollController _suggestionsScrollController = ScrollController();
  BookCaseController? bookCaseController;

  String _currentQuery = '';
  int _currentOffset = 0;
  final int _limit = 10;
  List<Books> _allSuggestions = [];
  bool _isLoadingMore = false;
  bool _hasMoreResults = true;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _suggestionsScrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _suggestionsScrollController.removeListener(_onScroll);
    _suggestionsScrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_suggestionsScrollController.position.pixels >=
            _suggestionsScrollController.position.maxScrollExtent - 50 &&
        !_isLoadingMore &&
        _hasMoreResults) {
      _loadMoreResults();
    }
  }

  Future<void> _loadMoreResults() async {
    if (_isLoadingMore || !_hasMoreResults || _currentQuery.isEmpty) return;

    setState(() {
      _isLoadingMore = true;
    });

    _currentOffset += _limit;

    try {
      final moreResults = await widget.fetchBooksCallback(
          _currentQuery, _currentOffset, _limit);

      if (moreResults.isEmpty) {
        _hasMoreResults = false;
      } else {
        setState(() {
          _allSuggestions.addAll(moreResults);
          if (widget.suggestionsController != null) {
            widget.suggestionsController!.refresh();
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  Future<List<Books>> _handleSuggestions(String pattern) async {
    _debounceTimer?.cancel();

    if (pattern.length < widget.minCharsForSuggestions) {
      _currentQuery = '';
      _allSuggestions = [];
      _currentOffset = 0;
      _hasMoreResults = true;
      return [];
    }

    final normalizedPattern = _textCleaner.removeAll(pattern.toLowerCase());

    // If query changed, reset pagination
    if (_currentQuery != normalizedPattern) {
      return await Future.delayed(const Duration(milliseconds: 300), () async {
        _currentQuery = normalizedPattern;
        _currentOffset = 0;
        _hasMoreResults = true;

        try {
          final results = await widget.fetchBooksCallback(
              normalizedPattern, _currentOffset, _limit);

          _allSuggestions = List.from(results);

          // Add "Can't find your book" option at the top
          if (widget.onCantFindBook != null) {
            _allSuggestions.insert(
              0,
              Books(
                bookName: "Can't Find Your Book? Click Here",
              ),
            );
          }

          return _allSuggestions;
        } catch (e) {
          log("Error fetching suggestions: $e");
          return [];
        }
      });
    }

    return _allSuggestions;
  }

  @override
  Widget build(BuildContext context) {
    return TypeAheadField<Books>(
      suggestionsController: widget.suggestionsController,
      controller: widget.controller,
      scrollController: _suggestionsScrollController,
      builder: (context, controller, focusNode) => _buildTextField(
        controller,
        focusNode,
      ),
      itemBuilder: (context, book) {
        if (book.bookName == "Can't Find Your Book? Click Here") {
          return _buildCantFindBookTile();
        } else {
          return _buildBookTile(book);
        }
      },
      onSelected: (book) {
        if (book.bookName == "Can't Find Your Book? Click Here" &&
            widget.onCantFindBook != null) {
          widget.onCantFindBook!();
        } else {
          widget.onSelected(book);
        }
      },
      suggestionsCallback: _handleSuggestions,
      decorationBuilder: _buildDecoration,
      loadingBuilder: _buildLoadingIndicator,
      emptyBuilder: _buildEmptyState,
      hideOnEmpty: true,
      debounceDuration: const Duration(milliseconds: 300),
    );
  }

  Widget _buildTextField(
    TextEditingController controller,
    FocusNode focusNode,
  ) {
    return TextFormField(
      inputFormatters: [
        controller.text.isEmpty
            ? FilteringTextInputFormatter.deny(RegExp(r'\s'))
            : FilteringTextInputFormatter.singleLineFormatter,
      ],
      controller: controller,
      focusNode: focusNode,
      style: lbRegular.copyWith(fontSize: 18),
      autofocus: widget.autofocus,
      textCapitalization: TextCapitalization.sentences,
      decoration: (widget.decoration ?? const InputDecoration()).copyWith(
        filled: true,
        fillColor: widget.isAddNewMeeting
            ? Colors.white
            : AppConstants.backgroundColor,
        contentPadding: const EdgeInsets.all(10),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: const BorderSide(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        suffixIcon: NetworkAwareTap(
          onTap: () => controller.text.isNotEmpty ? controller.clear() : null,
          child: Icon(
            controller.text.isEmpty
                ? Icons.search_rounded
                : Icons.clear_rounded,
            size: 25,
            color: AppConstants.primaryColor,
          ),
        ),
      ),
      onChanged: (value) {
        setState(() {
          bookCaseController?.updateTypeAheadFlag(false);
        });
      },
    );
  }

  Widget _buildDecoration(BuildContext context, Widget child) {
    return Material(
      type: MaterialType.card,
      elevation: 0,
      color:
          widget.isAddNewMeeting ? Colors.white : AppConstants.backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.primaryColor,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(child: child),
          if (_isLoadingMore)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 8.0),
              child: Center(
                child: SizedBox(
                  height: 40,
                  width: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        color: AppConstants.primaryColor,
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return ListTile(
      title: Text(
        'No books found!',
        style: lbRegular.copyWith(fontSize: 14),
      ),
    );
  }

  Widget _buildBookTile(Books book) {
    return ListTile(
      title: Text(
        book.bookName ?? '',
        style: lbRegular.copyWith(fontSize: 15),
      ),
      subtitle: Text(
        book.bookAuthor ?? '',
        style: lbBold.copyWith(
          fontSize: 14,
          fontWeight: FontWeight.w900,
        ),
      ),
      shape: const Border(
        bottom: BorderSide(color: AppConstants.primaryColor),
      ),
    );
  }

  Widget _buildCantFindBookTile() {
    return ListTile(
      title: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: "Can't Find Your Book? ",
              style: lbRegular.copyWith(
                fontSize: 15,
                color: AppConstants.primaryColor,
              ),
            ),
            TextSpan(
              text: "Click Here",
              style: lbBold.copyWith(
                fontSize: 15,
                color: AppConstants.blueColor,
              ),
            ),
          ],
        ),
      ),
      shape: const Border(
        bottom: BorderSide(
          color: AppConstants.primaryColor,
        ),
      ),
    );
  }
}
