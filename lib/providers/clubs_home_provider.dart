import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../constants/common_helper.dart';
import '../constants/constants.dart';
import '../controller/book_club_controller.dart';
import '../controller/message_controller.dart';
import '../controller/user_controller.dart';
import '../models/book_club_model.dart';
import '../models/clubs_model/clubs_screen4_model/incoming_outgoing_request.dart';
import '../reusable_api_function/club/club_function.dart';

class ClubsHomeProvider with ChangeNotifier {
  // Loading states
  bool _isInitializing = false;
  bool _hasError = false;
  String _errorMessage = '';

  // User data
  int? _loggedInUserId;
  String _userProfilePicture = '';
  String _userHandle = '';

  // Club data
  List<BookClubModel>? _standingBookClubList = [];
  List<BookClubModel>? _impromptuBookClubList = [];
  List<RequestManage>? _pendingInvitations;
  List<RequestManage>? _outgoingRequestList;

  // Controllers
  ClubController? _clubController;
  BookClubController? _bookClubController;
  MessageController? _messageController;
  UserController? _userController;

  // Getters
  bool get isInitializing => _isInitializing;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  int? get loggedInUserId => _loggedInUserId;
  String get userProfilePicture => _userProfilePicture;
  String get userHandle => _userHandle;
  List<BookClubModel>? get standingBookClubList => _standingBookClubList;
  List<BookClubModel>? get impromptuBookClubList => _impromptuBookClubList;
  List<RequestManage>? get pendingInvitations => _pendingInvitations;
  List<RequestManage>? get outgoingRequestList => _outgoingRequestList;
  ClubController? get clubController => _clubController;

  // Initialize the provider with context
  Future<void> initialize(BuildContext context) async {
    if (_isInitializing) return;

    _isInitializing = true;
    _hasError = false;
    _errorMessage = '';
    notifyListeners();

    try {
      // Get controllers
      _userController = Provider.of<UserController>(context, listen: false);
      _messageController =
          Provider.of<MessageController>(context, listen: false);
      _clubController = Provider.of<ClubController>(context, listen: false);
      _bookClubController =
          Provider.of<BookClubController>(context, listen: false);

      // Initialize user data and fetch all data
      await _initializeUserIdAndFetchData(context);
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Failed to load data: ${e.toString()}';
      log('ClubsHomeProvider initialization error: $e');
    } finally {
      _isInitializing = false;
      notifyListeners();
    }
  }

  // Refresh all data
  Future<void> refresh(BuildContext context) async {
    try {
      await _initializeUserIdAndFetchData(context);
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Failed to refresh data: ${e.toString()}';
      log('ClubsHomeProvider refresh error: $e');
      notifyListeners();
    }
  }

  // Initialize user ID and fetch all data
  Future<void> _initializeUserIdAndFetchData(BuildContext context) async {
    await _initializeUserId();
    if (context.mounted) {
      await Future.wait(
        [
          _getStandingBookClubsByUserId(false, context),
          _getImpromptuBookClubsByUserId(false, context),
          _getOutgoingRequest(context),
        ],
      );
    }
  }

  // Initialize user ID and get user details
  Future<void> _initializeUserId() async {
    _loggedInUserId = await CommonHelper.getLoggedInUserId();
    await _getUserDetails();
  }

  // Get user details
  Future<void> _getUserDetails() async {
    try {
      if (_userController != null && _loggedInUserId != null) {
        // Get user details without context since it's not needed for this call
        _userProfilePicture =
            _userController!.userModel.data?.userProfilePicture ?? '';
        _userHandle = _userController!.userModel.data?.userHandle ?? '';
      }
    } catch (e) {
      log('Error getting user details: $e');
    }
  }

  // Get standing book clubs
  Future<void> _getStandingBookClubsByUserId(
      bool isMore, BuildContext context) async {
    if (_clubController == null || _loggedInUserId == null) return;

    try {
      await _clubController!.getBookClubsByUserId(
        context,
        _loggedInUserId!,
        ClubType.standing,
        isMore,
      );

      _standingBookClubList = _clubController!.standingBookClubList;

      // Check for incoming requests
      bool hasNewStandingRequests = _standingBookClubList?.any((element) =>
              (element.incomingRequest) && element.userId == _loggedInUserId) ==
          true;

      if (_messageController != null) {
        await _messageController!
            .updateStandingClubRequests(hasNewStandingRequests);
      }
    } catch (e) {
      log('Error getting standing clubs: $e');
    }
  }

  // Get impromptu book clubs
  Future<void> _getImpromptuBookClubsByUserId(
      bool isMore, BuildContext context) async {
    if (_clubController == null || _loggedInUserId == null) return;

    try {
      await _clubController!.getBookClubsByUserId(
        context,
        _loggedInUserId!,
        ClubType.impromptu,
        isMore,
      );

      _impromptuBookClubList = _clubController!.impromptuBookClubList;

      // Check for incoming requests
      bool hasNewImpromptuRequests = _impromptuBookClubList?.any((element) =>
              (element.incomingRequest) && element.userId == _loggedInUserId) ==
          true;

      if (_messageController != null) {
        await _messageController!
            .updateImpromptuClubRequests(hasNewImpromptuRequests);
      }
    } catch (e) {
      log('Error getting impromptu clubs: $e');
    }
  }

  // Get outgoing requests
  Future<void> _getOutgoingRequest(BuildContext context) async {
    if (_bookClubController == null || _loggedInUserId == null) return;

    try {
      await _bookClubController!.inComingRequest(
        _loggedInUserId!,
        ClubMembershipStatus.pending,
        ClubMembershipStatus.reOpened,
        ClubRequestType.outgoingClubRequestByUserId,
        context,
      );

      _outgoingRequestList = _bookClubController!.incomingOutGoingRequest?.data;

      // Check status and update message controller
      bool hasNewOutgoingRequests = await _checkStatus();
      if (_messageController != null) {
        await _messageController!
            .updateOutgoingRequests(hasNewOutgoingRequests);
      }
    } catch (e) {
      log('Error getting outgoing requests: $e');
    }
  }

  // Check status for outgoing requests
  Future<bool> _checkStatus() async {
    if (_outgoingRequestList?.isNotEmpty ?? false) {
      for (var element in _outgoingRequestList!) {
        if (element.status == "REOPENED") {
          return true;
        }
      }
    }
    return false;
  }

  // Handle standing club scroll pagination
  Future<void> handleStandingClubScroll(BuildContext context) async {
    if (_clubController?.standingLoading == false &&
        (_clubController?.standingBookClubList?.length ?? 0) <
            (_clubController?.standingClubCount ?? 0)) {
      try {
        await _getStandingBookClubsByUserId(true, context);
      } catch (e) {
        log('Error loading more standing clubs: $e');
      }
    }
  }

  // Handle impromptu club scroll pagination
  Future<void> handleImpromptuClubScroll(BuildContext context) async {
    if (_clubController?.impromptuLoading == false &&
        (_clubController?.impromptuBookClubList?.length ?? 0) <
            (_clubController?.impromptuClubCount ?? 0)) {
      try {
        await _getImpromptuBookClubsByUserId(true, context);
      } catch (e) {
        log('Error loading more impromptu clubs: $e');
      }
    }
  }

  // Clear error state
  void clearError() {
    _hasError = false;
    _errorMessage = '';
    notifyListeners();
  }
}
